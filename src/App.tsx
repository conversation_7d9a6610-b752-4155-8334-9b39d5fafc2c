/**
 * ==============================================
 * MOREWISE - MEMECOIN TRADING BOT PLATFORM
 * ==============================================
 *
 * Complete memecoin trading bot platform with modern UI/UX.
 * Features bot marketplace, portfolio management, multi-crypto support,
 * and traditional exchange model (no wallet connections required).
 */

import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from './lib/icons';

// Import route protection components
import { ProtectedRoute, GuestRoute } from './components/RouteProtection';
import { LogoutConfirmationDialog } from './components/ConfirmationDialog';
import { initializeSessionManager } from './lib/sessionManager';



// Import new platform components
import { BotMarketplace } from './pages/BotMarketplace';
import { BotDetail } from './pages/BotDetail';
import { TradingBot } from './types';
import { EnhancedPortfolio } from './pages/EnhancedPortfolio';
import { DepositWithdraw } from './pages/DepositWithdraw';
import { TransactionHistory } from './pages/TransactionHistory';
import { UserSettings } from './pages/UserSettings';
import { EnhancedDashboard } from './pages/EnhancedDashboard';
import { BotSetup } from './pages/BotSetup';
import { BotManagement as UserBotManagement } from './pages/BotManagement';

import { InvestmentModal } from './components/InvestmentModal';
import { Header } from './components/Header';
import { MobileNavigation } from './components/MobileNavigation';

// Import legacy components for admin and auth
import { LandingPage } from './pages/LandingPage';
import { Auth } from './pages/Auth';

import { AuthProvider, useAuth } from './lib/AuthContext';

// Import admin components
import { AdminLogin } from './pages/admin/AdminLogin';
import { AdminDashboard } from './pages/admin/AdminDashboard';
import { UserManagement } from './pages/admin/UserManagement';
import { BotManagement } from './pages/admin/BotManagement';
import { WalletManagement } from './pages/admin/WalletManagement';
import { TransactionManagement } from './pages/admin/TransactionManagement';
import { SupportManagement } from './pages/admin/SupportManagement';
import { AdminLayout } from './components/AdminLayout';
import { ConvexClientProvider } from './lib/convex';

// User balances interface for the new platform
interface UserBalances extends Record<string, number> {
  USDT: number;
  USDC: number;
  BTC: number;
  ETH: number;
  DOGE: number;
  SHIB: number;
  PEPE: number;
}

// Bot investment interface for the new platform
interface BotInvestmentData {
  id: string;
  botName: string;
  riskLevel: 'low' | 'medium' | 'high';
  investedAmount: number;
  currentValue: number;
  totalReturn: number;
  returnPercent: number;
  dailyReturn: number;
  isActive: boolean;
  investmentDate: string;
}

type Page = 'dashboard' | 'marketplace' | 'bot-detail' | 'portfolio' | 'deposit-withdraw' | 'transactions' | 'settings' | 'bot-setup' | 'bot-management' | 'investment';



/**
 * ==============================================
 * MOREWISE MAIN PLATFORM COMPONENT
 * ==============================================
 *
 * Complete memecoin trading bot platform with modern UI/UX.
 * Handles navigation, state management, and all platform features.
 */
function MorewisePlatform() {
  const [currentPage, setCurrentPage] = useState<Page>('dashboard');
  const [showInvestmentModal, setShowInvestmentModal] = useState(false);
  const [selectedBot, setSelectedBot] = useState<Record<string, unknown> | null>(null);

  // Convert marketplace bot to TradingBot format
  const convertToTradingBot = (marketplaceBot: Record<string, unknown>): TradingBot => {
    return {
      id: marketplaceBot.id,
      name: marketplaceBot.name,
      description: marketplaceBot.description,
      strategy: {
        type: 'momentum',
        parameters: {},
        stop_loss_percentage: 5,
        take_profit_percentage: 15,
        max_trades_per_day: 10,
      },
      target_coins: marketplaceBot.supportedCoins || [],
      risk_level: marketplaceBot.riskLevel,
      min_investment: marketplaceBot.minInvestment,
      max_investment: marketplaceBot.maxInvestment,
      performance: {
        total_return_percentage: marketplaceBot.expectedReturn,
        win_rate: marketplaceBot.winRate,
        total_trades: 100,
        profitable_trades: Math.floor(100 * (Number(marketplaceBot.winRate) || 75) / 100),
        average_trade_duration: 24,
        max_drawdown: 10,
        sharpe_ratio: 1.5,
        last_30_days_return: marketplaceBot.performance30d,
        last_7_days_return: marketplaceBot.performance7d,
        historical_data: [],
      },
      is_active: marketplaceBot.isActive,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      expected_return: marketplaceBot.expectedReturn,
    } as any;
  };

  // User balances state (mock data - replace with real API)
  const [userBalances, setUserBalances] = useState<UserBalances>({
    USDT: 5000,
    USDC: 2500,
    BTC: 0.15,
    ETH: 2.5,
    DOGE: 10000,
    SHIB: 50000000,
    PEPE: 1000000,
  });

  // Bot investments state (mock data)
  const [botInvestments, setBotInvestments] = useState<BotInvestmentData[]>([
    {
      id: '1',
      botName: 'Conservative Growth',
      riskLevel: 'low',
      investedAmount: 2000,
      currentValue: 2156.80,
      totalReturn: 156.80,
      returnPercent: 7.84,
      dailyReturn: 12.50,
      isActive: true,
      investmentDate: '2024-03-10',
    },
    {
      id: '2',
      botName: 'Momentum Hunter',
      riskLevel: 'medium',
      investedAmount: 3000,
      currentValue: 3420.15,
      totalReturn: 420.15,
      returnPercent: 14.01,
      dailyReturn: 28.75,
      isActive: true,
      investmentDate: '2024-03-08',
    },
  ]);



  // Handle bot selection from marketplace
  const handleInvestClick = (bot: any) => {
    setSelectedBot(bot);
    setCurrentPage('bot-detail');
  };

  // Handle navigation from bot detail to investment page
  const handleNavigateToInvestment = () => {
    setCurrentPage('investment');
  };

  const handleInvestment = (_botId: string, amount: number, _autoReinvest: boolean) => {
    if (userBalances.USDT >= amount && selectedBot) {
      // Deduct from balance
      setUserBalances(prev => ({
        ...prev,
        USDT: prev.USDT - amount
      }));

      // Add new investment
      const newInvestment: BotInvestmentData = {
        id: Date.now().toString(),
        botName: String(selectedBot.name || ''),
        riskLevel: (selectedBot.riskLevel as 'low' | 'medium' | 'high') || 'medium',
        investedAmount: amount,
        currentValue: amount,
        totalReturn: 0,
        returnPercent: 0,
        dailyReturn: 0,
        isActive: true,
        investmentDate: new Date().toISOString().split('T')[0],
      };

      setBotInvestments(prev => [...prev, newInvestment]);
      setSelectedBot(null);
      setCurrentPage('bot-management'); // Navigate to bot management instead of portfolio
    }
  };

  // Handle deposits
  const handleDeposit = (currency: string, amount: number) => {
    setUserBalances(prev => ({
      ...prev,
      [currency]: prev[currency] + amount
    }));
  };

  // Handle withdrawals
  const handleWithdraw = (currency: string, amount: number) => {
    if (userBalances[currency] >= amount) {
      setUserBalances(prev => ({
        ...prev,
        [currency]: prev[currency] - amount
      }));
      return true;
    }
    return false;
  };

  // Navigation items for the platform
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: ICON_NAMES.HOME },
    { id: 'marketplace', label: 'Marketplace', icon: ICON_NAMES.ROBOT },
    { id: 'portfolio', label: 'Portfolio', icon: ICON_NAMES.WALLET },
    { id: 'deposit-withdraw', label: 'Funds', icon: ICON_NAMES.EXCHANGE },
    { id: 'transactions', label: 'History', icon: ICON_NAMES.CLOCK },
    { id: 'settings', label: 'Settings', icon: ICON_NAMES.SETTINGS },
  ];

  // Render current page content
  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <EnhancedDashboard
            userBalances={userBalances}
            botInvestments={botInvestments}
            onNavigate={(page: string) => setCurrentPage(page as Page)}
          />
        );
      case 'marketplace':
        return (
          <BotMarketplace
            onInvestClick={handleInvestClick}
            onSetupCustomBot={() => setCurrentPage('bot-setup')}
          />
        );
      case 'bot-detail':
        return selectedBot ? (
          <BotDetail
            tradingBots={[convertToTradingBot(selectedBot)]}
            onNavigateToInvestment={handleNavigateToInvestment}
            onBack={() => setCurrentPage('marketplace')}
          />
        ) : (
          <div className="flex items-center justify-center min-h-screen">
            <p>No bot selected</p>
          </div>
        );
      case 'portfolio':
        return (
          <EnhancedPortfolio
            onDepositClick={() => setCurrentPage('deposit-withdraw')}
            onWithdrawClick={() => setCurrentPage('deposit-withdraw')}
            onBotClick={() => setCurrentPage('marketplace')}
          />
        );
      case 'deposit-withdraw':
        return (
          <DepositWithdraw
            userBalances={userBalances}
            onDeposit={handleDeposit}
            onWithdraw={handleWithdraw}
          />
        );
      case 'transactions':
        return <TransactionHistory />;
      case 'settings':
        return <UserSettings />;
      case 'bot-setup':
        return (
          <BotSetup
            onComplete={(botConfig) => {
              console.log('Bot setup completed:', botConfig);
              setCurrentPage('bot-management');
            }}
            onCancel={() => setCurrentPage('marketplace')}
          />
        );
      case 'bot-management':
        return (
          <UserBotManagement
            onBackToMarketplace={() => setCurrentPage('marketplace')}
            userInvestments={botInvestments}
          />
        );
      case 'investment':
        return selectedBot ? (
          <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-4">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-between mb-8">
                <button
                  onClick={() => setCurrentPage('bot-detail')}
                  className="flex items-center text-white hover:text-purple-300 transition-colors"
                >
                  <FontAwesomeIcon icon={ICON_NAMES.BACK} className="mr-2" />
                  Back
                </button>
                <h1 className="text-3xl font-bold text-white">Invest in {String(selectedBot.name || '')}</h1>
                <div className="w-16"></div>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
                <p className="text-white text-center">Investment functionality coming soon...</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center min-h-screen">
            <p>No bot selected</p>
          </div>
        );
default:
        return (
          <div className="p-4 md:p-6 pb-32 md:pb-6">
            <div className="text-center">
              <h1 className="text-3xl font-black text-gray-900 mb-2">Welcome to Morewise</h1>
              <p className="text-gray-600">Your memecoin trading bot platform</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <Header onNavigate={(page: string) => setCurrentPage(page as Page)} currentPage={currentPage} />

      {/* Desktop Sidebar Navigation */}
      <nav className="hidden md:flex fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-white backdrop-blur-lg border-r border-gray-200 z-40 flex-col shadow-xl">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-8">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-lg" />
            </div>
            <div className="flex-1">
              <h1 className="text-xl font-black text-gray-900">Morewise</h1>
              <p className="text-xs text-gray-500">Trading Platform</p>
            </div>
          </div>

          <div className="space-y-2">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentPage(item.id as Page)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl font-medium transition-all ${
                  currentPage === item.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <FontAwesomeIcon icon={item.icon} className="text-lg" />
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* User Info */}
        <div className="mt-auto p-6 border-t border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
              <FontAwesomeIcon icon={ICON_NAMES.USER} className="text-white" />
            </div>
            <div>
              <p className="font-semibold text-gray-900">Demo User</p>
              <p className="text-sm text-gray-600">Premium Account</p>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto md:ml-64 pt-4">
        {renderCurrentPage()}
      </main>



      {/* Investment Modal */}
      {showInvestmentModal && selectedBot && (
        <InvestmentModal
          isOpen={showInvestmentModal}
          bot={selectedBot as any}
          userBalance={userBalances.USDT}
          onConfirm={(amount: number, autoReinvest: boolean) =>
            handleInvestment(String(selectedBot.id || ''), amount, autoReinvest)
          }
          onClose={() => {
            setShowInvestmentModal(false);
            setSelectedBot(null);
          }}
        />
      )}

      {/* Mobile Navigation */}
      <MobileNavigation onNavigate={(page: string) => setCurrentPage(page as Page)} currentPage={currentPage} />

      {/* Desktop Content Offset - handled by CSS classes */}
    </div>
  );
}

/**
 * Main application content component
 * Routes to the new Morewise platform or legacy auth pages
 */
function AppContent() {
  const { user, loading, signOut } = useAuth();
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Initialize session manager on app start
  useEffect(() => {
    initializeSessionManager();
  }, []);

  // Handle logout confirmation
  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await signOut();
      setShowLogoutDialog(false);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Routes>
        {/* Public routes */}
        <Route
          path="/"
          element={
            <GuestRoute onUnauthorized={() => <Navigate to="/dashboard" />}>
              <LandingPage />
            </GuestRoute>
          }
        />
        <Route
          path="/login"
          element={
            <GuestRoute onUnauthorized={() => <Navigate to="/dashboard" />}>
              <Auth onAuthSuccess={() => {}} />
            </GuestRoute>
          }
        />
        <Route
          path="/signup"
          element={
            <GuestRoute onUnauthorized={() => <Navigate to="/dashboard" />}>
              <Auth onAuthSuccess={() => {}} />
            </GuestRoute>
          }
        />

        {/* Protected routes - require authentication */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />
        <Route
          path="/portfolio"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />
        <Route
          path="/marketplace"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />
        <Route
          path="/wallet"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />
        <Route
          path="/transactions"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />
        <Route
          path="/settings"
          element={
            <ProtectedRoute onUnauthorized={() => <Navigate to="/login" />}>
              <MorewisePlatform />
            </ProtectedRoute>
          }
        />

        {/* Redirect based on authentication status */}
        <Route
          path="*"
          element={
            user ? <Navigate to="/dashboard" /> : <Navigate to="/login" />
          }
        />
      </Routes>

      {/* Logout confirmation dialog */}
      <LogoutConfirmationDialog
        isOpen={showLogoutDialog}
        onClose={() => setShowLogoutDialog(false)}
        onConfirm={handleLogout}
        loading={isLoggingOut}
      />
    </>
  );
}



// Admin Content Component
function AdminContent() {
  const navigate = useNavigate();
  const location = useLocation();

  // Handle admin login success
  const handleAdminLoginSuccess = () => {
    navigate('/admin/dashboard');
  };

  // Handle admin logout
  const handleAdminLogout = () => {
    navigate('/admin/login');
  };



  const renderAdminPage = () => {
    const path = location.pathname;

    if (path === '/admin/login') {
      return <AdminLogin onLoginSuccess={handleAdminLoginSuccess} />;
    }

    if (path === '/admin/users') {
      return (
        <AdminLayout currentPage="users" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <UserManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/bots') {
      return (
        <AdminLayout currentPage="bots" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <BotManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/wallets') {
      return (
        <AdminLayout currentPage="wallets" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <WalletManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/transactions') {
      return (
        <AdminLayout currentPage="transactions" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <TransactionManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/support') {
      return (
        <AdminLayout currentPage="support" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <SupportManagement />
        </AdminLayout>
      );
    }

    if (path === '/admin/analytics') {
      return (
        <AdminLayout currentPage="analytics" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Analytics Dashboard</h2>
            <p className="text-gray-400">Advanced analytics coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/logs') {
      return (
        <AdminLayout currentPage="logs" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Audit Logs</h2>
            <p className="text-gray-400">Audit logging coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    if (path === '/admin/settings') {
      return (
        <AdminLayout currentPage="settings" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">System Settings</h2>
            <p className="text-gray-400">System configuration coming soon...</p>
          </div>
        </AdminLayout>
      );
    }

    // Default to dashboard
    return (
      <AdminLayout currentPage="dashboard" onNavigate={(page) => navigate(`/admin/${page}`)} onLogout={handleAdminLogout}>
        <AdminDashboard />
      </AdminLayout>
    );
  };

  return renderAdminPage();
}

function App() {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div>
      {isAdminRoute ? <AdminContent /> : <AppContent />}
    </div>
  );
}

function AppWrapper() {
  return (
    <ConvexClientProvider>
      <Router>
        <AuthProvider>
          <App />
        </AuthProvider>
      </Router>
    </ConvexClientProvider>
  );
}

export default AppWrapper;