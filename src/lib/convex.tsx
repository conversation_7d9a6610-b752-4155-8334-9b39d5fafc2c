import { ConvexProvider, ConvexReactClient } from "convex/react";
import { ReactNode } from "react";

// Initialize Convex client with the real URL from environment
const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL!);

// Provider component for wrapping the app
export function ConvexClientProvider({ children }: { children: ReactNode }) {
  return <ConvexProvider client={convex}>{children}</ConvexProvider>;
}

export { convex };
export default convex;
