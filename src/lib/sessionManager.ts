/**
 * ==============================================
 * SESSION MANAGER
 * ==============================================
 * 
 * This module handles session management with secure cookies,
 * automatic cleanup, and session validation.
 */

import { Id } from '../../convex/_generated/dataModel';

// Session data interface
interface SessionData {
  userId: Id<"users">;
  sessionId: Id<"user_sessions">;
  email: string;
  username: string;
  name: string;
  is_admin: boolean;
  admin_role?: string;
  admin_permissions: string[];
  expires_at: number;
  created_at: number;
}

// Cookie configuration
const COOKIE_CONFIG = {
  SESSION_COOKIE: 'morewise_session',
  ADMIN_COOKIE: 'morewise_admin_session',
  EXPIRES_DAYS: 7, // Session expires in 7 days
  SECURE: window.location.protocol === 'https:', // Use secure cookies in production
  SAME_SITE: 'strict' as const,
};

/**
 * Set a secure cookie with proper configuration
 */
function setCookie(name: string, value: string, days: number): void {
  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  
  const cookieOptions = [
    `${name}=${encodeURIComponent(value)}`,
    `expires=${expires.toUTCString()}`,
    'path=/',
    `SameSite=${COOKIE_CONFIG.SAME_SITE}`,
  ];

  if (COOKIE_CONFIG.SECURE) {
    cookieOptions.push('Secure');
  }

  // Note: HttpOnly cannot be set from client-side JavaScript
  // In production, set HttpOnly cookies from server-side

  document.cookie = cookieOptions.join('; ');
}

/**
 * Get cookie value by name
 */
function getCookie(name: string): string | null {
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
  }
  return null;
}

/**
 * Delete a cookie
 */
function deleteCookie(name: string): void {
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=${COOKIE_CONFIG.SAME_SITE}`;
}

/**
 * Create and store session data
 */
export function createSession(userData: {
  userId: Id<"users">;
  sessionId: Id<"user_sessions">;
  email: string;
  username: string;
  name: string;
  is_admin: boolean;
  admin_role?: string;
  admin_permissions: string[];
}): void {
  const now = Date.now();
  const expiresAt = now + (COOKIE_CONFIG.EXPIRES_DAYS * 24 * 60 * 60 * 1000);

  const sessionData: SessionData = {
    ...userData,
    expires_at: expiresAt,
    created_at: now,
  };

  // Store in secure cookie
  setCookie(COOKIE_CONFIG.SESSION_COOKIE, JSON.stringify(sessionData), COOKIE_CONFIG.EXPIRES_DAYS);

  // Store admin session separately if user is admin
  if (userData.is_admin) {
    const adminData = {
      userId: userData.userId,
      sessionId: userData.sessionId,
      role: userData.admin_role,
      permissions: userData.admin_permissions,
      expires_at: expiresAt,
    };
    setCookie(COOKIE_CONFIG.ADMIN_COOKIE, JSON.stringify(adminData), COOKIE_CONFIG.EXPIRES_DAYS);
  }

  // Also store in localStorage as backup (less secure but more accessible)
  localStorage.setItem('user_session', JSON.stringify(sessionData));
  localStorage.setItem('session_id', userData.sessionId);
}

/**
 * Get current session data
 */
export function getSession(): SessionData | null {
  try {
    // Try to get from cookie first
    const cookieData = getCookie(COOKIE_CONFIG.SESSION_COOKIE);
    if (cookieData) {
      const sessionData: SessionData = JSON.parse(cookieData);
      
      // Check if session is expired
      if (sessionData.expires_at > Date.now()) {
        return sessionData;
      } else {
        // Session expired, clean up
        clearSession();
        return null;
      }
    }

    // Fallback to localStorage
    const localData = localStorage.getItem('user_session');
    if (localData) {
      const sessionData: SessionData = JSON.parse(localData);
      
      // Check if session is expired
      if (sessionData.expires_at > Date.now()) {
        return sessionData;
      } else {
        // Session expired, clean up
        clearSession();
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting session:', error);
    clearSession();
    return null;
  }
}

/**
 * Get admin session data
 */
export function getAdminSession(): { userId: Id<"users">; sessionId: Id<"user_sessions">; role?: string; permissions: string[] } | null {
  try {
    const adminCookie = getCookie(COOKIE_CONFIG.ADMIN_COOKIE);
    if (adminCookie) {
      const adminData = JSON.parse(adminCookie);
      
      // Check if session is expired
      if (adminData.expires_at > Date.now()) {
        return adminData;
      } else {
        // Session expired, clean up
        clearAdminSession();
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting admin session:', error);
    clearAdminSession();
    return null;
  }
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
  const session = getSession();
  return session !== null;
}

/**
 * Check if user is admin
 */
export function isAdmin(): boolean {
  const session = getSession();
  const adminSession = getAdminSession();
  return session?.is_admin === true && adminSession !== null;
}

/**
 * Check if admin has specific permission
 */
export function hasAdminPermission(permission: string): boolean {
  const adminSession = getAdminSession();
  if (!adminSession) return false;
  
  // Super admin has all permissions
  if (adminSession.role === 'super_admin') return true;
  
  return adminSession.permissions.includes(permission);
}

/**
 * Update session data (extend expiry, update user info)
 */
export function updateSession(updates: Partial<SessionData>): void {
  const currentSession = getSession();
  if (!currentSession) return;

  const updatedSession = {
    ...currentSession,
    ...updates,
    expires_at: Date.now() + (COOKIE_CONFIG.EXPIRES_DAYS * 24 * 60 * 60 * 1000), // Extend expiry
  };

  // Update cookies and localStorage
  setCookie(COOKIE_CONFIG.SESSION_COOKIE, JSON.stringify(updatedSession), COOKIE_CONFIG.EXPIRES_DAYS);
  localStorage.setItem('user_session', JSON.stringify(updatedSession));

  // Update admin session if applicable
  if (updatedSession.is_admin) {
    const adminData = {
      userId: updatedSession.userId,
      sessionId: updatedSession.sessionId,
      role: updatedSession.admin_role,
      permissions: updatedSession.admin_permissions,
      expires_at: updatedSession.expires_at,
    };
    setCookie(COOKIE_CONFIG.ADMIN_COOKIE, JSON.stringify(adminData), COOKIE_CONFIG.EXPIRES_DAYS);
  }
}

/**
 * Clear all session data
 */
export function clearSession(): void {
  // Delete cookies
  deleteCookie(COOKIE_CONFIG.SESSION_COOKIE);
  deleteCookie(COOKIE_CONFIG.ADMIN_COOKIE);
  
  // Clear localStorage
  localStorage.removeItem('user_session');
  localStorage.removeItem('session_id');
  localStorage.removeItem('admin_session');
}

/**
 * Clear only admin session
 */
export function clearAdminSession(): void {
  deleteCookie(COOKIE_CONFIG.ADMIN_COOKIE);
  localStorage.removeItem('admin_session');
}

/**
 * Auto cleanup expired sessions (call periodically)
 */
export function cleanupExpiredSessions(): void {
  const session = getSession();
  if (!session) {
    clearSession();
  }
}

/**
 * Initialize session manager (call on app startup)
 */
export function initializeSessionManager(): void {
  // Clean up any expired sessions on startup
  cleanupExpiredSessions();
  
  // Set up periodic cleanup (every 5 minutes)
  setInterval(cleanupExpiredSessions, 5 * 60 * 1000);
  
  // Clean up on page visibility change (when user returns to tab)
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      cleanupExpiredSessions();
    }
  });
}
