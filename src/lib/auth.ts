/**
 * ==============================================
 * AUTHENTICATION UTILITIES
 * ==============================================
 *
 * This module provides authentication utility functions.
 * Uses the AuthContext for actual authentication operations.
 */

import { Id } from '../../convex/_generated/dataModel';

// User interface matching Convex schema
export interface User {
  id: Id<"users">;
  email: string;
  username: string;
  name: string;
  is_verified: boolean;
  is_admin: boolean;
  admin_role?: string;
  admin_permissions: string[];
}

export interface LoginResult {
  success: boolean;
  user?: User;
  error?: string;
}

export interface RegisterResult {
  success: boolean;
  user?: User;
  error?: string;
}

/**
 * Check if user is authenticated by checking localStorage
 * For real-time auth state, use the AuthContext instead
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const savedUser = localStorage.getItem('user_session');
    return savedUser !== null;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
}

/**
 * Get current user from localStorage
 * For real-time user data, use the AuthContext instead
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    const savedUser = localStorage.getItem('user_session');
    if (savedUser) {
      return JSON.parse(savedUser);
    }
    return null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Clear user session (logout)
 * For proper logout, use the AuthContext signOut method instead
 */
export async function logout(): Promise<void> {
  try {
    localStorage.removeItem('user_session');
    localStorage.removeItem('session_id');
  } catch (error) {
    console.error('Logout error:', error);
  }
}

// Legacy function aliases for backward compatibility
// These should be replaced with AuthContext methods
export const login = async (email: string, password: string): Promise<LoginResult> => {
  console.warn('Using deprecated login function. Use AuthContext.signIn instead.');
  return { success: false, error: 'Use AuthContext.signIn instead' };
};

export const register = async (email: string, password: string, username: string): Promise<RegisterResult> => {
  console.warn('Using deprecated register function. Use AuthContext.signUp instead.');
  return { success: false, error: 'Use AuthContext.signUp instead' };
};

export const signup = register;
