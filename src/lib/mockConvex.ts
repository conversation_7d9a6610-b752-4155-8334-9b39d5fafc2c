// Mock Convex implementation for development
// This provides mock data when Convex backend is not available

export const mockUsers = [
  {
    _id: "user1",
    email: "<EMAIL>",
    username: "john_doe",
    name: "<PERSON>",
    is_verified: true,
    is_suspended: false,
    kyc_status: "approved",
    referral_code: "JOHN123",
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T10:00:00Z",
    last_login: "2024-01-20T15:30:00Z",
    portfolios: [{
      _id: "portfolio1",
      user_id: "user1",
      total_balance: 1500.50,
      available_balance: 1200.00,
      invested_balance: 300.50,
      total_profit_loss: 45.25,
      currency: "USDT",
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-20T15:30:00Z",
    }],
    bot_investments: [],
    user_sessions: [{
      _id: "session1",
      user_id: "user1",
      ip_address: "*************",
      user_agent: "Mozilla/5.0...",
      is_active: true,
      created_at: "2024-01-20T15:30:00Z",
      last_activity: "2024-01-20T16:45:00Z",
    }],
    user_activities: [{
      _id: "activity1",
      user_id: "user1",
      activity_type: "login",
      description: "User logged in",
      created_at: "2024-01-20T15:30:00Z",
    }],
    referrals_as_referrer: [],
    referrals_as_referred: [],
  },
  {
    _id: "user2",
    email: "<EMAIL>",
    username: "jane_smith",
    name: "Jane Smith",
    is_verified: false,
    is_suspended: false,
    kyc_status: "pending",
    referral_code: "JANE456",
    created_at: "2024-01-18T14:20:00Z",
    updated_at: "2024-01-18T14:20:00Z",
    last_login: "2024-01-19T09:15:00Z",
    portfolios: [{
      _id: "portfolio2",
      user_id: "user2",
      total_balance: 750.00,
      available_balance: 750.00,
      invested_balance: 0,
      total_profit_loss: 0,
      currency: "USDT",
      created_at: "2024-01-18T14:20:00Z",
      updated_at: "2024-01-18T14:20:00Z",
    }],
    bot_investments: [],
    user_sessions: [{
      _id: "session2",
      user_id: "user2",
      ip_address: "*************",
      user_agent: "Mozilla/5.0...",
      is_active: false,
      created_at: "2024-01-19T09:15:00Z",
      last_activity: "2024-01-19T10:30:00Z",
      ended_at: "2024-01-19T10:30:00Z",
    }],
    user_activities: [{
      _id: "activity2",
      user_id: "user2",
      activity_type: "registration",
      description: "User registered",
      created_at: "2024-01-18T14:20:00Z",
    }],
    referrals_as_referrer: [],
    referrals_as_referred: [],
  }
];

export const mockAdminStats = {
  users: {
    total: 2,
    verified: 1,
    suspended: 0,
    newToday: 0,
    newThisWeek: 1,
    newThisMonth: 2,
  },
  transactions: {
    total: 5,
    pending: 1,
    completed: 4,
    failed: 0,
    totalVolume: 2500.75,
    todayVolume: 150.00,
  },
  investments: {
    total: 3,
    active: 2,
    totalValue: 1250.50,
    totalProfit: 75.25,
  },
  sessions: {
    total: 8,
    active: 1,
    todayLogins: 1,
  },
};

export const mockPlatformMetrics = {
  dailyMetrics: Array.from({ length: 30 }, (_, i) => ({
    date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    users: Math.floor(Math.random() * 10),
    transactions: Math.floor(Math.random() * 20),
    volume: Math.floor(Math.random() * 1000),
    investments: Math.floor(Math.random() * 5),
  })).reverse(),
  summary: {
    totalUsers: 2,
    totalTransactions: 5,
    totalVolume: 2500.75,
    totalInvestments: 3,
    totalInvestmentValue: 1250.50,
  },
};
