/**
 * ==============================================
 * AUTH CONTEXT
 * ==============================================
 *
 * This context provides authentication state management across the application.
 * Uses Convex for real authentication and user management.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import {
  createSession,
  getSession,
  clearSession,
  updateSession,
  initializeSessionManager,
  cleanupExpiredSessions
} from './sessionManager';

// User interface matching Convex schema
interface User {
  id: Id<"users">;
  email: string;
  username: string;
  name: string;
  is_verified: boolean;
  is_admin: boolean;
  admin_role?: string;
  admin_permissions: string[];
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<boolean>;
  signUp: (email: string, username: string, name: string, password: string, referralCode?: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionId, setSessionId] = useState<Id<"user_sessions"> | null>(null);

  // Convex mutations
  const loginUser = useMutation(api.auth.loginUser);
  const registerUser = useMutation(api.auth.registerUser);
  const logoutUser = useMutation(api.auth.logoutUser);

  // Get user data if we have a user ID
  const userData = useQuery(
    api.auth.getUser,
    user ? { userId: user.id } : "skip"
  );

  useEffect(() => {
    // Initialize session manager on app startup
    initializeSessionManager();

    // Check for existing session using session manager
    const checkSession = () => {
      try {
        const session = getSession();

        if (session) {
          setUser({
            id: session.userId,
            email: session.email,
            username: session.username,
            name: session.name,
            is_verified: true, // Will be updated from userData
            is_admin: session.is_admin,
            admin_role: session.admin_role,
            admin_permissions: session.admin_permissions,
          });
          setSessionId(session.sessionId);
        }
      } catch (error) {
        console.error('Error loading session:', error);
        clearSession();
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Set up periodic session cleanup
    const cleanupInterval = setInterval(cleanupExpiredSessions, 5 * 60 * 1000); // Every 5 minutes

    return () => {
      clearInterval(cleanupInterval);
    };
  }, []);

  // Update user data when it changes
  useEffect(() => {
    if (userData && user) {
      // Check if the user data has actually changed to prevent infinite loops
      const hasChanged =
        userData.is_verified !== user.is_verified ||
        userData.is_admin !== user.is_admin ||
        userData.admin_role !== user.admin_role ||
        JSON.stringify(userData.admin_permissions) !== JSON.stringify(user.admin_permissions);

      if (hasChanged) {
        const updatedUser = { ...user, ...userData };
        setUser(updatedUser);

        // Update session with new user data
        updateSession({
          userId: updatedUser.id,
          email: updatedUser.email,
          username: updatedUser.username,
          name: updatedUser.name,
          is_admin: updatedUser.is_admin,
          admin_role: updatedUser.admin_role,
          admin_permissions: updatedUser.admin_permissions,
        });
      }
    }
  }, [userData]);

  const signIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);

      const result = await loginUser({
        email,
        password,
        ipAddress: undefined, // Could get from browser
        userAgent: navigator.userAgent,
      });

      if (result.success && result.user) {
        setUser(result.user);
        setSessionId(result.sessionId);

        // Create secure session with cookies
        createSession({
          userId: result.user.id,
          sessionId: result.sessionId,
          email: result.user.email,
          username: result.user.username,
          name: result.user.name,
          is_admin: result.user.is_admin,
          admin_role: result.user.admin_role,
          admin_permissions: result.user.admin_permissions,
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Error during sign in:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string,
    username: string,
    name: string,
    password: string,
    referralCode?: string
  ): Promise<boolean> => {
    try {
      setLoading(true);

      const result = await registerUser({
        email,
        username,
        name,
        password,
        referralCode,
      });

      return result.success;
    } catch (error) {
      console.error('Error during sign up:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      if (sessionId) {
        await logoutUser({ sessionId });
      }

      setUser(null);
      setSessionId(null);

      // Clear all session data including cookies
      clearSession();
    } catch (error) {
      console.error('Error during sign out:', error);
      // Even if logout fails, clear local session
      clearSession();
    }
  };

  const value = {
    user,
    loading,
    signOut,
    signIn,
    signUp,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}