/**
 * ==============================================
 * CONFIRMATION DIALOG COMPONENT
 * ==============================================
 * 
 * Reusable confirmation dialog for important actions like logout,
 * account deletion, data clearing, etc.
 */

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info';
  loading?: boolean;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'warning',
  loading = false,
}) => {
  if (!isOpen) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'danger':
        return {
          icon: ICON_NAMES.WARNING,
          iconColor: 'text-red-400',
          confirmButton: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
          backdrop: 'from-red-900/20 via-gray-900/50 to-red-900/20',
        };
      case 'info':
        return {
          icon: ICON_NAMES.INFO,
          iconColor: 'text-blue-400',
          confirmButton: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
          backdrop: 'from-blue-900/20 via-gray-900/50 to-blue-900/20',
        };
      default: // warning
        return {
          icon: ICON_NAMES.WARNING,
          iconColor: 'text-yellow-400',
          confirmButton: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
          backdrop: 'from-yellow-900/20 via-gray-900/50 to-yellow-900/20',
        };
    }
  };

  const styles = getTypeStyles();

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'Enter' && !loading) {
      onConfirm();
    }
  };

  return (
    <div 
      className={`fixed inset-0 bg-gradient-to-br ${styles.backdrop} backdrop-blur-sm z-50 flex items-center justify-center p-4`}
      onClick={handleBackdropClick}
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 max-w-md w-full border border-white/20 shadow-2xl">
        {/* Header */}
        <div className="flex items-center mb-4">
          <div className={`${styles.iconColor} text-2xl mr-3`}>
            <FontAwesomeIcon icon={styles.icon} />
          </div>
          <h2 className="text-xl font-bold text-white">{title}</h2>
        </div>

        {/* Message */}
        <p className="text-gray-300 mb-6 leading-relaxed">{message}</p>

        {/* Actions */}
        <div className="flex gap-3 justify-end">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className={`px-4 py-2 ${styles.confirmButton} text-white rounded-lg transition-colors focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2`}
          >
            {loading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            )}
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Logout confirmation dialog
 */
export const LogoutConfirmationDialog: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}> = ({ isOpen, onClose, onConfirm, loading }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    title="Confirm Logout"
    message="Are you sure you want to log out? You will need to sign in again to access your account."
    confirmText="Log Out"
    cancelText="Stay Logged In"
    type="warning"
    loading={loading}
  />
);

/**
 * Account deletion confirmation dialog
 */
export const DeleteAccountConfirmationDialog: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}> = ({ isOpen, onClose, onConfirm, loading }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    title="Delete Account"
    message="This action cannot be undone. All your data, investments, and account history will be permanently deleted."
    confirmText="Delete Account"
    cancelText="Keep Account"
    type="danger"
    loading={loading}
  />
);

/**
 * Admin action confirmation dialog
 */
export const AdminActionConfirmationDialog: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  actionText: string;
  loading?: boolean;
}> = ({ isOpen, onClose, onConfirm, title, message, actionText, loading }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    title={title}
    message={message}
    confirmText={actionText}
    cancelText="Cancel"
    type="danger"
    loading={loading}
  />
);

/**
 * Data clearing confirmation dialog
 */
export const ClearDataConfirmationDialog: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  dataType: string;
  loading?: boolean;
}> = ({ isOpen, onClose, onConfirm, dataType, loading }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    title={`Clear ${dataType}`}
    message={`Are you sure you want to clear all ${dataType.toLowerCase()}? This action cannot be undone.`}
    confirmText={`Clear ${dataType}`}
    cancelText="Keep Data"
    type="warning"
    loading={loading}
  />
);

/**
 * Session timeout warning dialog
 */
export const SessionTimeoutDialog: React.FC<{
  isOpen: boolean;
  onExtend: () => void;
  onLogout: () => void;
  timeRemaining: number;
}> = ({ isOpen, onExtend, onLogout, timeRemaining }) => {
  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;

  return (
    <ConfirmationDialog
      isOpen={isOpen}
      onClose={onExtend}
      onConfirm={onLogout}
      title="Session Expiring"
      message={`Your session will expire in ${minutes}:${seconds.toString().padStart(2, '0')}. Do you want to extend your session?`}
      confirmText="Log Out"
      cancelText="Extend Session"
      type="warning"
    />
  );
};
