/**
 * ==============================================
 * HEADER COMPONENT
 * ==============================================
 * Top header with hamburger menu for mobile navigation
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface HeaderProps {
  onNavigate: (page: string) => void;
  currentPage: string;
}

export const Header: React.FC<HeaderProps> = ({ onNavigate, currentPage }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: ICON_NAMES.HOME },
    { id: 'marketplace', label: 'Bot Marketplace', icon: ICON_NAMES.ROBOT },
    { id: 'portfolio', label: 'Portfolio', icon: ICON_NAMES.WALLET },
    { id: 'deposit-withdraw', label: 'Deposit/Withdraw', icon: ICON_NAMES.EXCHANGE },
    { id: 'transactions', label: 'Transactions', icon: ICON_NAMES.HISTORY },
    { id: 'settings', label: 'Settings', icon: ICON_NAMES.SETTINGS },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleMenuClick = (pageId: string) => {
    onNavigate(pageId);
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Header */}
      <header className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-lg" />
              </div>
              <h1 className="text-xl font-black bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Morewise
              </h1>
            </div>

            {/* Desktop Navigation - Hidden on mobile */}
            <nav className="hidden md:flex space-x-8">
              {menuItems.slice(0, 5).map((item) => (
                <button
                  key={item.id}
                  onClick={() => onNavigate(item.id)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    currentPage === item.id
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:text-purple-600 hover:bg-gray-100'
                  }`}
                >
                  <FontAwesomeIcon icon={item.icon} className="text-sm" />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>

            {/* Mobile Hamburger Menu Button */}
            <button
              onClick={toggleMenu}
              className="md:hidden p-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-gray-100 transition-colors duration-200"
            >
              <FontAwesomeIcon
                icon={isMenuOpen ? ICON_NAMES.CLOSE : ICON_NAMES.MENU}
                className="text-xl"
              />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="fixed inset-0 bg-black bg-opacity-50" onClick={toggleMenu}></div>
          <div className="fixed top-16 left-0 right-0 bg-white shadow-xl border-b border-gray-200">
            <nav className="px-4 py-4 space-y-2">
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleMenuClick(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left font-medium transition-colors duration-200 ${
                    currentPage === item.id
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-700 hover:text-purple-600 hover:bg-gray-100'
                  }`}
                >
                  <FontAwesomeIcon icon={item.icon} className="text-lg" />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}
    </>
  );
};