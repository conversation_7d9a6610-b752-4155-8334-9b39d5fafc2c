/**
 * ==============================================
 * MOBILE NAVIGATION COMPONENT
 * ==============================================
 * Floating bottom navigation for mobile devices
 */

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';

interface MobileNavigationProps {
  onNavigate: (page: string) => void;
  currentPage: string;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({ onNavigate, currentPage }) => {
  const navItems = [
    { id: 'dashboard', label: 'Home', icon: ICON_NAMES.HOME },
    { id: 'marketplace', label: 'Bots', icon: ICON_NAMES.ROBOT },
    { id: 'portfolio', label: 'Portfolio', icon: ICON_NAMES.WALLET },
    { id: 'transactions', label: 'History', icon: ICON_NAMES.HISTORY },
  ];

  return (
    <div className="fixed bottom-4 left-4 right-4 md:hidden z-50">
      <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 px-2 py-3">
        <div className="flex justify-around items-center">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onNavigate(item.id)}
              className={`flex flex-col items-center space-y-1 px-3 py-2 rounded-xl transition-all duration-200 ${
                currentPage === item.id
                  ? 'bg-purple-100 text-purple-700'
                  : 'text-gray-600 hover:text-purple-600 hover:bg-gray-100'
              }`}
            >
              <FontAwesomeIcon icon={item.icon} className="text-lg" />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
