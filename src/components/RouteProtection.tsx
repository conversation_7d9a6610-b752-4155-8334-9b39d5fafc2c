/**
 * ==============================================
 * PROTECTED ROUTE COMPONENTS
 * ==============================================
 * 
 * These components handle route protection based on authentication
 * and authorization levels.
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '../lib/AuthContext';
import { isAuthenticated, isAdmin, hasAdminPermission, getSession } from '../lib/sessionManager';

// Loading spinner component
const LoadingSpinner: React.FC = () => (
  <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
      <p className="text-white text-lg">Loading...</p>
    </div>
  </div>
);

// Unauthorized access component
const UnauthorizedAccess: React.FC<{ message?: string; onRedirect?: () => void }> = ({ 
  message = "You don't have permission to access this page.", 
  onRedirect 
}) => (
  <div className="min-h-screen bg-gradient-to-br from-gray-900 via-red-900 to-gray-900 flex items-center justify-center p-4">
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-md w-full text-center">
      <div className="text-red-400 text-6xl mb-4">🚫</div>
      <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
      <p className="text-gray-300 mb-6">{message}</p>
      {onRedirect && (
        <button
          onClick={onRedirect}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
        >
          Go Back
        </button>
      )}
    </div>
  </div>
);

// Props for protected route components
interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onUnauthorized?: () => void;
}

interface AdminRouteProps extends ProtectedRouteProps {
  requiredPermission?: string;
  requiredRole?: string;
}

/**
 * Basic authentication protection
 * Requires user to be logged in
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback,
  onUnauthorized 
}) => {
  const { user, loading } = useAuth();
  const [sessionValid, setSessionValid] = useState<boolean | null>(null);

  useEffect(() => {
    // Check session validity
    const checkSession = () => {
      const authenticated = isAuthenticated();
      const session = getSession();
      
      // Verify session matches current user
      if (authenticated && session && user && session.userId === user.id) {
        setSessionValid(true);
      } else if (authenticated && !user) {
        // Session exists but user not loaded yet, wait
        setSessionValid(null);
      } else {
        setSessionValid(false);
      }
    };

    checkSession();
  }, [user]);

  // Show loading while checking authentication
  if (loading || sessionValid === null) {
    return fallback || <LoadingSpinner />;
  }

  // User not authenticated
  if (!sessionValid || !user) {
    if (onUnauthorized) {
      onUnauthorized();
      return null;
    }
    
    return (
      <UnauthorizedAccess 
        message="Please log in to access this page."
        onRedirect={onUnauthorized}
      />
    );
  }

  // User authenticated, render children
  return <>{children}</>;
};

/**
 * Admin-only route protection
 * Requires user to be logged in AND have admin privileges
 */
export const AdminRoute: React.FC<AdminRouteProps> = ({ 
  children, 
  fallback,
  onUnauthorized,
  requiredPermission,
  requiredRole 
}) => {
  const { user, loading } = useAuth();
  const [authStatus, setAuthStatus] = useState<'loading' | 'authorized' | 'unauthorized' | 'forbidden'>('loading');

  useEffect(() => {
    const checkAdminAccess = () => {
      // First check basic authentication
      if (!isAuthenticated() || !user) {
        setAuthStatus('unauthorized');
        return;
      }

      // Check admin status
      if (!isAdmin() || !user.is_admin) {
        setAuthStatus('forbidden');
        return;
      }

      // Check specific permission if required
      if (requiredPermission && !hasAdminPermission(requiredPermission)) {
        setAuthStatus('forbidden');
        return;
      }

      // Check specific role if required
      if (requiredRole && user.admin_role !== requiredRole && user.admin_role !== 'super_admin') {
        setAuthStatus('forbidden');
        return;
      }

      setAuthStatus('authorized');
    };

    if (!loading) {
      checkAdminAccess();
    }
  }, [user, loading, requiredPermission, requiredRole]);

  // Show loading while checking
  if (loading || authStatus === 'loading') {
    return fallback || <LoadingSpinner />;
  }

  // Handle different unauthorized states
  if (authStatus === 'unauthorized') {
    if (onUnauthorized) {
      onUnauthorized();
      return null;
    }
    
    return (
      <UnauthorizedAccess 
        message="Please log in to access the admin panel."
        onRedirect={onUnauthorized}
      />
    );
  }

  if (authStatus === 'forbidden') {
    const message = requiredPermission 
      ? `Admin access required. Missing permission: ${requiredPermission}`
      : requiredRole
      ? `Admin access required. Required role: ${requiredRole}`
      : "Admin privileges required to access this page.";
      
    return (
      <UnauthorizedAccess 
        message={message}
        onRedirect={onUnauthorized}
      />
    );
  }

  // User authorized, render children
  return <>{children}</>;
};

/**
 * Guest-only route protection
 * Redirects authenticated users away from login/signup pages
 */
export const GuestRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback,
  onUnauthorized 
}) => {
  const { user, loading } = useAuth();

  // Show loading while checking
  if (loading) {
    return fallback || <LoadingSpinner />;
  }

  // User is authenticated, redirect away
  if (user && isAuthenticated()) {
    if (onUnauthorized) {
      onUnauthorized();
      return null;
    }
    
    // Could redirect to dashboard or show a message
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-md w-full text-center">
          <div className="text-green-400 text-6xl mb-4">✅</div>
          <h1 className="text-2xl font-bold text-white mb-4">Already Logged In</h1>
          <p className="text-gray-300 mb-6">You are already authenticated.</p>
        </div>
      </div>
    );
  }

  // User not authenticated, show guest content
  return <>{children}</>;
};

/**
 * Permission-based component wrapper
 * Shows content only if user has specific permission
 */
export const PermissionGate: React.FC<{
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ permission, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user || !isAdmin() || !hasAdminPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Role-based component wrapper
 * Shows content only if user has specific role
 */
export const RoleGate: React.FC<{
  role: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}> = ({ role, children, fallback = null }) => {
  const { user } = useAuth();

  if (!user || !user.is_admin || (user.admin_role !== role && user.admin_role !== 'super_admin')) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
