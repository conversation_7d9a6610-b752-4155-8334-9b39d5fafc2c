/**
 * ==============================================
 * WALLET MANAGEMENT PAGE
 * ==============================================
 *
 * This component handles real deposit/withdraw functionality with
 * multi-wallet system integration and real database operations.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { WalletBalance, User } from '../types';
import { formatCurrency } from '../lib/utils';
// Wallet data now uses Convex directly
import { CryptoIcon } from '../components/CryptoIcon';

interface Cryptocurrency {
  id: string;
  symbol: string;
  name: string;
  network: string;
  decimals: number;
  min_deposit: number;
  icon_url: string;
  is_active: boolean;
}

interface UserWallet {
  id: string;
  address: string;
  cryptocurrency: Cryptocurrency;
  bundle_id: string;
}

interface DepositTransaction {
  id: string;
  amount: number;
  cryptocurrency: Cryptocurrency;
  wallet_address: string;
  status: 'pending' | 'confirming' | 'confirmed' | 'failed' | 'expired';
  transaction_hash?: string;
  confirmations: number;
  required_confirmations: number;
  expires_at: string;
  created_at: string;
}

interface WalletProps {
  walletBalance: WalletBalance;
  user: User;
}

export const Wallet: React.FC<WalletProps> = ({
  walletBalance,
  user
}) => {

  const [userWallets, setUserWallets] = useState<UserWallet[]>([]);
  const [supportedCryptos, setSupportedCryptos] = useState<Cryptocurrency[]>([]);
  const [activeDeposit, setActiveDeposit] = useState<DepositTransaction | null>(null);
  const [selectedCrypto, setSelectedCrypto] = useState<Cryptocurrency | null>(null);
  const [depositAmount, setDepositAmount] = useState<string>('');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [depositStep, setDepositStep] = useState<'amount' | 'crypto' | 'address'>('amount');
  const [validationError, setValidationError] = useState<string>('');
  const [isValidAmount, setIsValidAmount] = useState(false);
  const [isGeneratingAddress, setIsGeneratingAddress] = useState(false);


  // Load real wallet data on component mount
  useEffect(() => {
    loadWalletData();
  }, [user.id]);

  // Countdown timer for active deposits
  useEffect(() => {
    if (activeDeposit && activeDeposit.expires_at) {
      const interval = setInterval(() => {
        const now = new Date().getTime();
        const expiry = new Date(activeDeposit.expires_at).getTime();
        const remaining = Math.max(0, expiry - now);
        setTimeRemaining(remaining);

        if (remaining === 0) {
          setActiveDeposit(null);
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [activeDeposit]);

  /**
   * Load wallet data using mock data
   */
  const loadWalletData = async () => {
    try {
      setLoading(true);

      // Mock supported cryptocurrencies
      const mockCryptos = [
        { id: '1', symbol: 'SOL', name: 'Solana', is_active: true, network: 'solana', decimals: 9, min_deposit: 0.01, icon_url: '/icons/sol.png' },
        { id: '2', symbol: 'BTC', name: 'Bitcoin', is_active: true, network: 'bitcoin', decimals: 8, min_deposit: 0.0001, icon_url: '/icons/btc.png' },
        { id: '3', symbol: 'ETH', name: 'Ethereum', is_active: true, network: 'ethereum', decimals: 18, min_deposit: 0.001, icon_url: '/icons/eth.png' },
        { id: '4', symbol: 'USDT', name: 'Tether', is_active: true, network: 'ethereum', decimals: 6, min_deposit: 1, icon_url: '/icons/usdt.png' },
        { id: '5', symbol: 'USDC', name: 'USD Coin', is_active: true, network: 'ethereum', decimals: 6, min_deposit: 1, icon_url: '/icons/usdc.png' },
      ];

      setSupportedCryptos(mockCryptos);
      if (mockCryptos.length > 0) {
        setSelectedCrypto(mockCryptos[0]);
      }

      // Mock user wallets using the same crypto objects
      const mockWallets = [
        { id: '1', address: 'DemoSolanaAddress123...', cryptocurrency_id: '1', cryptocurrency: mockCryptos[0], bundle_id: 'demo-bundle' },
        { id: '2', address: 'DemoBitcoinAddress456...', cryptocurrency_id: '2', cryptocurrency: mockCryptos[1], bundle_id: 'demo-bundle' },
        { id: '3', address: 'DemoEthereumAddress789...', cryptocurrency_id: '3', cryptocurrency: mockCryptos[2], bundle_id: 'demo-bundle' },
        { id: '4', address: 'DemoUSDTAddress012...', cryptocurrency_id: '4', cryptocurrency: mockCryptos[3], bundle_id: 'demo-bundle' },
        { id: '5', address: 'DemoUSDCAddress345...', cryptocurrency_id: '5', cryptocurrency: mockCryptos[4], bundle_id: 'demo-bundle' },
      ];
      setUserWallets(mockWallets);

      // Mock active deposit (none for demo)
      setActiveDeposit(null);

      setLoading(false);
    } catch (error) {
      console.error('Error loading wallet data:', error);
      setLoading(false);
    }
  };

  /**
   * Validate deposit amount
   */
  const validateAmount = (amount: string, crypto?: any) => {
    if (!amount || amount === '') {
      setValidationError('');
      setIsValidAmount(false);
      return false;
    }

    const numAmount = parseFloat(amount);

    if (isNaN(numAmount) || numAmount <= 0) {
      setValidationError('Please enter a valid amount');
      setIsValidAmount(false);
      return false;
    }

    if (crypto && numAmount < crypto.min_deposit) {
      setValidationError(`Minimum deposit is ${crypto.min_deposit} ${crypto.symbol}`);
      setIsValidAmount(false);
      return false;
    }

    setValidationError('');
    setIsValidAmount(true);
    return true;
  };

  /**
   * Handle amount input change with validation
   */
  const handleAmountChange = (value: string) => {
    setDepositAmount(value);
    validateAmount(value, selectedCrypto);
  };

  /**
   * Create a new deposit transaction with enhanced loading states
   */
  const handleCreateDeposit = async () => {
    if (!selectedCrypto || !depositAmount || !user.id) return;

    const amount = parseFloat(depositAmount);
    if (amount < selectedCrypto.min_deposit) {
      alert(`Minimum deposit is ${selectedCrypto.min_deposit} ${selectedCrypto.symbol}`);
      return;
    }

    try {
      setLoading(true);
      setIsGeneratingAddress(true);

      // Simulate address generation delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Find user's wallet for this cryptocurrency
      const userWallet = userWallets.find(w => w.cryptocurrency.id === selectedCrypto.id);
      if (!userWallet) {
        alert('No wallet found for this cryptocurrency. Please contact support.');
        setLoading(false);
        setIsGeneratingAddress(false);
        return;
      }

      // Mock deposit creation
      const mockDeposit = {
        id: `deposit-${Date.now()}`,
        amount: amount,
        cryptocurrency: selectedCrypto,
        wallet_address: userWallet.address,
        status: 'pending' as const,
        transaction_hash: undefined,
        confirmations: 0,
        required_confirmations: 3,
        expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString()
      };

      setActiveDeposit(mockDeposit);
      setDepositAmount('');
      alert(`Demo: Deposit address generated for ${amount} ${selectedCrypto.symbol}`);

      setLoading(false);
      setIsGeneratingAddress(false);
    } catch (error) {
      console.error('Error creating deposit:', error);
      alert('Failed to create deposit. Please try again.');
      setLoading(false);
      setIsGeneratingAddress(false);
    }
  };

  const formatTime = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };



  return (
    <div className="w-full min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 px-4 py-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-xl font-bold text-gray-900 mb-1">Wallet</h1>
          <p className="text-gray-600 text-sm">Manage your cryptocurrency</p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-4 space-y-4">
        {/* Total Balance Card */}
        <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm font-medium mb-2">Total Balance</p>
              <h2 className="text-4xl font-black tracking-tight">{formatCurrency(walletBalance.usd + walletBalance.eth * 2000 + walletBalance.btc * 45000 + (walletBalance.sol || 0) * 100)}</h2>
            </div>
            <button
              onClick={() => setShowDepositModal(!showDepositModal)}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 hover:scale-105 rounded-xl px-4 py-3 font-semibold transition-all duration-300 shadow-lg"
            >
              <FontAwesomeIcon icon={ICON_NAMES.PLUS} className="mr-2" />
              Deposit
            </button>
          </div>
        </div>

        {/* Balance List */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-lg font-bold text-gray-900">Your Balances</h3>
          </div>

          <div className="divide-y divide-gray-100">
            {/* SOL Balance - DOMINANT */}
            <div className="p-5 flex items-center justify-between bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 transition-all duration-300 cursor-pointer group">
              <div className="flex items-center space-x-4">
                <div className="transform group-hover:scale-110 transition-transform duration-300">
                  <CryptoIcon symbol="SOL" size="lg" />
                </div>
                <div>
                  <div className="font-bold text-gray-900 text-lg">Solana</div>
                  <div className="text-sm text-purple-600 font-semibold flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2 animate-pulse"></span>
                    SOL • Main
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-black text-gray-900 text-xl">{(walletBalance.sol || 0).toFixed(4)} SOL</div>
                <div className="text-sm text-gray-600 font-medium">{formatCurrency((walletBalance.sol || 0) * 100)}</div>
              </div>
            </div>

            {/* BTC Balance */}
            <div className="p-4 flex items-center justify-between hover:bg-gray-50 transition-all duration-300 cursor-pointer group">
              <div className="flex items-center space-x-3">
                <div className="transform group-hover:scale-110 transition-transform duration-300">
                  <CryptoIcon symbol="BTC" size="md" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-base">Bitcoin</div>
                  <div className="text-sm text-gray-500">BTC</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-gray-900 text-lg">{walletBalance.btc.toFixed(6)} BTC</div>
                <div className="text-sm text-gray-600 font-medium">{formatCurrency(walletBalance.btc * 45000)}</div>
              </div>
            </div>

            {/* ETH Balance */}
            <div className="p-4 flex items-center justify-between hover:bg-gray-50 transition-all duration-300 cursor-pointer group">
              <div className="flex items-center space-x-3">
                <div className="transform group-hover:scale-110 transition-transform duration-300">
                  <CryptoIcon symbol="ETH" size="md" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-base">Ethereum</div>
                  <div className="text-sm text-gray-500">ETH</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-gray-900 text-lg">{walletBalance.eth.toFixed(4)} ETH</div>
                <div className="text-sm text-gray-600 font-medium">{formatCurrency(walletBalance.eth * 2000)}</div>
              </div>
            </div>

            {/* USDT Balance */}
            <div className="p-4 flex items-center justify-between hover:bg-gray-50 transition-all duration-300 cursor-pointer group">
              <div className="flex items-center space-x-3">
                <div className="transform group-hover:scale-110 transition-transform duration-300">
                  <CryptoIcon symbol="USDT" size="md" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-base">Tether USD</div>
                  <div className="text-sm text-gray-500">USDT</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-gray-900 text-lg">{formatCurrency(walletBalance.usd)}</div>
                <div className="text-sm text-gray-600 font-medium">USDT</div>
              </div>
            </div>

            {/* USDC Balance */}
            <div className="p-4 flex items-center justify-between hover:bg-gray-50 transition-all duration-300 cursor-pointer group">
              <div className="flex items-center space-x-3">
                <div className="transform group-hover:scale-110 transition-transform duration-300">
                  <CryptoIcon symbol="USDC" size="md" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900 text-base">USD Coin</div>
                  <div className="text-sm text-gray-500">USDC</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-gray-900 text-lg">{formatCurrency(walletBalance.usdc || 0)}</div>
                <div className="text-sm text-gray-600 font-medium">USDC</div>
              </div>
            </div>
          </div>
        </div>

        {/* Deposit Section */}
        {showDepositModal && (
          <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden animate-fadeIn">
            <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-blue-50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-gray-900">Make a Deposit</h3>
                <div className="flex items-center space-x-2">
                  {depositStep === 'amount' && <div className="w-2 h-2 bg-purple-500 rounded-full"></div>}
                  {depositStep === 'crypto' && <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>}
                  {depositStep === 'address' && <div className="w-2 h-2 bg-green-500 rounded-full"></div>}
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                </div>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {!activeDeposit ? (
                <>
                  {/* Step 1: Enter Amount */}
                  {depositStep === 'amount' && (
                    <div className="space-y-6 animate-slideIn">
                      <div className="text-center">
                        <h4 className="text-xl font-bold text-gray-900 mb-2">Enter Deposit Amount</h4>
                        <p className="text-gray-600">How much would you like to deposit?</p>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-3">
                            Amount
                          </label>
                          <div className="relative">
                            <input
                              type="number"
                              value={depositAmount}
                              onChange={(e) => handleAmountChange(e.target.value)}
                              placeholder="0.00"
                              className={`w-full px-4 py-4 text-2xl font-bold border-2 rounded-xl focus:ring-2 focus:ring-purple-500 transition-all duration-300 ${
                                validationError
                                  ? 'border-red-300 focus:border-red-500'
                                  : isValidAmount
                                    ? 'border-green-300 focus:border-green-500'
                                    : 'border-gray-200 focus:border-purple-500'
                              }`}
                            />
                            {isValidAmount && (
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <FontAwesomeIcon icon={ICON_NAMES.CHECK} className="text-green-500 text-xl" />
                              </div>
                            )}
                          </div>

                          {validationError && (
                            <div className="mt-2 text-red-600 text-sm font-medium animate-shake">
                              <FontAwesomeIcon icon={ICON_NAMES.ALERT} className="mr-1" />
                              {validationError}
                            </div>
                          )}

                          {!validationError && depositAmount && (
                            <div className="mt-2 text-gray-600 text-sm">
                              <FontAwesomeIcon icon={ICON_NAMES.INFO} className="mr-1" />
                              Ready to proceed with ${depositAmount}
                            </div>
                          )}
                        </div>

                        <button
                          onClick={() => setDepositStep('crypto')}
                          disabled={!isValidAmount}
                          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-400 text-white font-bold py-4 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 shadow-lg"
                        >
                          {isValidAmount ? (
                            <>
                              <FontAwesomeIcon icon={ICON_NAMES.ARROW_RIGHT} className="mr-2" />
                              Continue to Crypto Selection
                            </>
                          ) : (
                            'Enter Amount to Continue'
                          )}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Step 2: Select Cryptocurrency */}
                  {depositStep === 'crypto' && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900">Select Cryptocurrency</h4>
                        <button
                          onClick={() => setDepositStep('amount')}
                          className="text-purple-600 hover:text-purple-700 text-xs font-medium"
                        >
                          Back
                        </button>
                      </div>
                      <div className="space-y-2">
                        {supportedCryptos.map((crypto) => (
                          <button
                            key={crypto.id}
                            onClick={() => {
                              setSelectedCrypto(crypto);
                              setDepositStep('address');
                              handleCreateDeposit();
                            }}
                            className="w-full p-2 border border-gray-200 hover:border-purple-300 rounded-lg flex items-center space-x-2 transition-colors"
                          >
                            <CryptoIcon symbol={crypto.symbol} size="sm" />
                            <div className="text-left">
                              <div className="font-medium text-gray-900 text-sm">{crypto.symbol}</div>
                              <div className="text-xs text-gray-500">{crypto.name}</div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                /* Step 3: Deposit Address & Countdown */
                <div className="space-y-4">
                  {/* Countdown Timer */}
                  <div className="text-center bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg p-4 text-white">
                    <div className="text-2xl font-bold mb-1">
                      {formatTime(timeRemaining)}
                    </div>
                    <p className="text-purple-100 text-xs">Time remaining</p>
                  </div>

                  {/* Deposit Details */}
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <CryptoIcon symbol={activeDeposit.cryptocurrency.symbol} size="sm" />
                      <div>
                        <div className="font-medium text-gray-900 text-sm">
                          {activeDeposit.amount} {activeDeposit.cryptocurrency.symbol}
                        </div>
                        <div className="text-xs text-gray-500">Deposit Amount</div>
                      </div>
                    </div>
                  </div>

                  {/* Wallet Address */}
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Send to this address:
                    </label>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="font-mono text-xs break-all mb-2 text-gray-700">
                        {activeDeposit.wallet_address}
                      </div>
                      <button
                        onClick={() => copyToClipboard(activeDeposit.wallet_address)}
                        className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 rounded-lg transition-colors text-sm"
                      >
                        <FontAwesomeIcon icon={ICON_NAMES.COPY} className="mr-1" />
                        Copy Address
                      </button>
                    </div>
                  </div>

                  {/* Warning */}
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-start space-x-2">
                      <FontAwesomeIcon icon={ICON_NAMES.ALERT} className="text-yellow-600 mt-0.5 text-xs" />
                      <div className="text-xs text-yellow-700">
                        <p className="font-medium mb-1">Important:</p>
                        <p>Only send {activeDeposit.cryptocurrency.symbol} to this address.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Recent Transactions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-3 border-b border-gray-100">
            <h3 className="text-sm font-semibold text-gray-900">Recent Transactions</h3>
          </div>

          <div className="p-4">
            <div className="text-center py-6">
              <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <FontAwesomeIcon icon={ICON_NAMES.CLOCK} className="text-lg text-gray-400" />
              </div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">No Transactions Yet</h4>
              <p className="text-gray-600 text-xs">
                Your transaction history will appear here once you make your first deposit.
              </p>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};
