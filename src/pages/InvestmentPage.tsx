import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { formatCurrency, formatPercentage } from '../lib/utils';
import { TradingBot } from '../types';

interface InvestmentPageProps {
  tradingBots: TradingBot[];
  userBalance: number;
  onConfirmInvestment: (botId: string, amount: number, autoReinvest: boolean) => void;
  onBack: () => void;
}

export const InvestmentPage: React.FC<InvestmentPageProps> = ({ 
  tradingBots, 
  userBalance, 
  onConfirmInvestment, 
  onBack 
}) => {
  const navigate = useNavigate();
  const [bot, setBot] = useState<TradingBot | null>(null);
  const [amount, setAmount] = useState(0);
  const [autoReinvest, setAutoReinvest] = useState(true);
  const [step, setStep] = useState(1);
  const [agreedToRisks, setAgreedToRisks] = useState(false);

  useEffect(() => {
    if (tradingBots && tradingBots.length > 0) {
      const foundBot = tradingBots[0];
      setBot(foundBot || null);
      if (foundBot) {
        setAmount(foundBot.min_investment);
      }
    }
  }, [tradingBots]);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleAmountChange = (newAmount: number) => {
    if (bot) {
      const clampedAmount = Math.max(
        bot.min_investment,
        Math.min(newAmount, Math.min(bot.max_investment, userBalance))
      );
      setAmount(clampedAmount);
    }
  };

  const handleNext = () => {
    if (step < 2) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleConfirm = () => {
    if (bot && amount > 0 && agreedToRisks) {
      onConfirmInvestment(bot.id, amount, autoReinvest);
    }
  };

  if (!bot) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="text-4xl text-purple-500 animate-spin mb-4" />
          <p className="text-gray-600">Loading bot details...</p>
        </div>
      </div>
    );
  }

  const expectedReturn = (bot as any).expected_return || 15;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack || (() => navigate(-1))}
            className="flex items-center text-white hover:text-purple-300 transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.BACK} className="mr-2" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-white">Invest in {bot.name}</h1>
          <div className="w-16"></div>
        </div>

        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-600 text-gray-300'
            }`}>
              1
            </div>
            <div className={`h-1 w-16 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-600'}`}></div>
            <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
              step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-600 text-gray-300'
            }`}>
              2
            </div>
          </div>
        </div>

        {step === 1 && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-8">
            <h2 className="text-2xl font-bold text-white mb-6">Investment Amount</h2>
            
            <div className="bg-white/5 rounded-xl p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white">{bot.name}</h3>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(bot.risk_level)}`}>
                  {bot.risk_level.toUpperCase()} RISK
                </span>
              </div>
              <p className="text-gray-300 mb-4">{bot.description}</p>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Expected Return</p>
                  <p className="text-green-400 font-bold">{formatPercentage(expectedReturn)}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Win Rate</p>
                  <p className="text-blue-400 font-bold">{formatPercentage((bot as any).win_rate || 75)}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-400 text-sm">Min Investment</p>
                  <p className="text-white font-bold">{formatCurrency(bot.min_investment)}</p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-white text-sm font-medium mb-2">
                Investment Amount
              </label>
              <div className="relative mb-4">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</div>
                <input
                  type="number"
                  value={amount || ''}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleAmountChange(Number(e.target.value))}
                  className="w-full pl-8 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter amount"
                  min={bot.min_investment}
                  max={Math.min(bot.max_investment, userBalance)}
                />
              </div>

              <input
                type="range"
                min={bot.min_investment}
                max={Math.min(bot.max_investment, userBalance)}
                value={amount}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleAmountChange(Number(e.target.value))}
                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
              />

              <div className="flex justify-between text-sm text-gray-400 mt-2">
                <span>{formatCurrency(bot.min_investment)}</span>
                <span>{formatCurrency(Math.min(bot.max_investment, userBalance))}</span>
              </div>
            </div>

            <div className="grid grid-cols-4 gap-2 mb-6">
              {[25, 50, 75, 100].map((percentage) => {
                const quickAmount = Math.floor((userBalance * percentage) / 100);
                const validAmount = Math.max(bot.min_investment, Math.min(quickAmount, bot.max_investment));
                return (
                  <button
                    key={percentage}
                    onClick={() => handleAmountChange(validAmount)}
                    className="py-2 px-4 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm"
                  >
                    {percentage}%
                  </button>
                );
              })}
            </div>

            <div className="mb-6">
              <label className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoReinvest}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setAutoReinvest(e.target.checked)}
                  className="w-5 h-5 text-purple-600 bg-white/10 border-white/20 rounded focus:ring-purple-500"
                />
                <span className="text-white">
                  Auto-reinvest profits (recommended for compound growth)
                </span>
              </label>
            </div>

            <div className="bg-white/5 rounded-xl p-4 mb-6">
              <h4 className="text-white font-semibold mb-3">Investment Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Investment Amount:</span>
                  <span className="text-white font-medium">{formatCurrency(amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Expected Monthly Return:</span>
                  <span className="text-green-400 font-medium">
                    {formatCurrency((amount * expectedReturn) / 100 / 12)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Remaining Balance:</span>
                  <span className="text-white font-medium">{formatCurrency(userBalance - amount)}</span>
                </div>
              </div>
            </div>

            <button
              onClick={handleNext}
              disabled={amount < bot.min_investment || amount > userBalance}
              className="w-full py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-lg transition-colors"
            >
              Continue to Confirmation
            </button>
          </div>
        )}

        {step === 2 && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Confirm Investment</h2>
            
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-6">
              <h3 className="text-red-400 font-semibold mb-3 flex items-center">
                <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="mr-2" />
                Risk Disclosure
              </h3>
              <div className="text-gray-300 text-sm space-y-2">
                <p>• Trading involves substantial risk and may result in loss of capital</p>
                <p>• Past performance does not guarantee future results</p>
                <p>• You may lose some or all of your invested capital</p>
                <p>• Only invest what you can afford to lose</p>
              </div>
              
              <label className="flex items-start space-x-3 cursor-pointer mt-4">
                <input
                  type="checkbox"
                  checked={agreedToRisks}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setAgreedToRisks(e.target.checked)}
                  className="w-5 h-5 text-red-600 bg-white/10 border-white/20 rounded focus:ring-red-500 mt-0.5"
                />
                <span className="text-white text-sm">
                  I understand and acknowledge the risks involved in automated trading
                </span>
              </label>
            </div>

            <div className="bg-white/5 rounded-xl p-6 mb-6">
              <h4 className="text-white font-semibold mb-4">Final Investment Details</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Trading Bot:</span>
                  <span className="text-white font-medium">{bot.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Investment Amount:</span>
                  <span className="text-white font-medium">{formatCurrency(amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Auto-reinvest:</span>
                  <span className="text-white font-medium">{autoReinvest ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Risk Level:</span>
                  <span className={`font-medium ${
                    bot.risk_level === 'low' ? 'text-green-400' :
                    bot.risk_level === 'medium' ? 'text-blue-400' : 'text-red-400'
                  }`}>
                    {bot.risk_level.toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex space-x-4">
              <button
                onClick={handleBack}
                className="flex-1 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-colors"
              >
                Back
              </button>
              <button
                onClick={handleConfirm}
                disabled={!agreedToRisks}
                className="flex-1 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-semibold rounded-lg transition-colors"
              >
                Confirm Investment
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
