/**
 * ==============================================
 * DEPOSIT & WITHDRAW PAGE
 * ==============================================
 * 
 * Complete deposit and withdrawal interface supporting multiple cryptocurrencies.
 * Features QR codes, address copying, and transaction history.
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components, animations } from '../lib/designSystem';
import { cryptoIcons, assetUtils } from '../lib/cryptoAssets';
import { formatCurrency } from '../lib/utils';

interface SupportedCrypto {
  symbol: string;
  name: string;
  network: string;
  minDeposit: number;
  minWithdraw: number;
  withdrawFee: number;
  depositAddress: string;
  isActive: boolean;
}

const SUPPORTED_CRYPTOS: SupportedCrypto[] = [
  {
    symbol: 'USDT',
    name: 'Tether',
    network: 'TRC20',
    minDeposit: 10,
    minWithdraw: 20,
    withdrawFee: 1,
    depositAddress: 'TQrZ9dxpKKKKKKKKKKKKKKKKKKKKKKKKKK',
    isActive: true,
  },
  {
    symbol: 'USDC',
    name: 'USD Coin',
    network: 'ERC20',
    minDeposit: 10,
    minWithdraw: 20,
    withdrawFee: 2,
    depositAddress: '******************************************',
    isActive: true,
  },
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    network: 'Bitcoin',
    minDeposit: 0.001,
    minWithdraw: 0.002,
    withdrawFee: 0.0005,
    depositAddress: '******************************************',
    isActive: true,
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    network: 'ERC20',
    minDeposit: 0.01,
    minWithdraw: 0.02,
    withdrawFee: 0.005,
    depositAddress: '******************************************',
    isActive: true,
  },
];

interface DepositWithdrawProps {
  onDeposit: (crypto: string, amount: number) => void;
  onWithdraw: (crypto: string, amount: number, address: string) => void;
}

export const DepositWithdraw: React.FC<DepositWithdrawProps> = ({
  onDeposit,
  onWithdraw,
}) => {
  const [activeTab, setActiveTab] = useState<'deposit' | 'withdraw'>('deposit');
  const [selectedCrypto, setSelectedCrypto] = useState(SUPPORTED_CRYPTOS[0]);
  const [withdrawAmount, setWithdrawAmount] = useState(0);
  const [withdrawAddress, setWithdrawAddress] = useState('');
  const [showQR, setShowQR] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const generateQRCode = (address: string) => {
    // In a real app, you'd use a QR code library
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${address}`;
  };

  const handleWithdraw = () => {
    if (withdrawAmount >= selectedCrypto.minWithdraw && withdrawAddress.trim()) {
      onWithdraw(selectedCrypto.symbol, withdrawAmount, withdrawAddress);
      setWithdrawAmount(0);
      setWithdrawAddress('');
    }
  };

  const maxWithdrawAmount = (userBalances[selectedCrypto.symbol] || 0) - selectedCrypto.withdrawFee;
  const isValidWithdraw = withdrawAmount >= selectedCrypto.minWithdraw && 
                         withdrawAmount <= maxWithdrawAmount && 
                         withdrawAddress.trim().length > 0;

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-black text-gray-900 mb-4">
          Deposit & Withdraw
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Fund your account or withdraw your profits securely
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-100 max-w-md mx-auto">
        <div className="grid grid-cols-2 gap-2">
          {['deposit', 'withdraw'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              className={`py-3 px-6 rounded-xl font-semibold transition-all ${
                activeTab === tab
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Crypto Selection */}
      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Select Cryptocurrency</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {SUPPORTED_CRYPTOS.map((crypto) => (
            <button
              key={crypto.symbol}
              onClick={() => setSelectedCrypto(crypto)}
              className={`p-4 rounded-2xl border-2 transition-all ${
                selectedCrypto.symbol === crypto.symbol
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div 
                  className="w-10 h-10 flex items-center justify-center"
                  dangerouslySetInnerHTML={{ 
                    __html: assetUtils.getCryptoIcon(crypto.symbol.toLowerCase()) 
                  }}
                />
                <div className="text-left">
                  <div className="font-bold text-gray-900">{crypto.symbol}</div>
                  <div className="text-xs text-gray-600">{crypto.network}</div>
                  <div className="text-xs text-gray-500">
                    Balance: {formatCurrency(userBalances[crypto.symbol] || 0)}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Deposit Section */}
      {activeTab === 'deposit' && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center space-x-3 mb-6">
            <div 
              className="w-12 h-12 flex items-center justify-center"
              dangerouslySetInnerHTML={{ 
                __html: assetUtils.getCryptoIcon(selectedCrypto.symbol.toLowerCase()) 
              }}
            />
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Deposit {selectedCrypto.symbol}
              </h3>
              <p className="text-sm text-gray-600">
                Network: {selectedCrypto.network} • Min: {formatCurrency(selectedCrypto.minDeposit)}
              </p>
            </div>
          </div>

          {/* Deposit Address */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deposit Address
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-1 p-3 bg-gray-50 rounded-xl border border-gray-200 font-mono text-sm break-all">
                  {selectedCrypto.depositAddress}
                </div>
                <button
                  onClick={() => copyToClipboard(selectedCrypto.depositAddress)}
                  className="p-3 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors"
                >
                  <FontAwesomeIcon icon={ICON_NAMES.COPY} />
                </button>
              </div>
            </div>

            {/* QR Code */}
            <div className="text-center">
              <button
                onClick={() => setShowQR(!showQR)}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors text-sm font-medium"
              >
                <FontAwesomeIcon icon={ICON_NAMES.QR_CODE} />
                <span>{showQR ? 'Hide' : 'Show'} QR Code</span>
              </button>
              
              {showQR && (
                <div className="mt-4 inline-block p-4 bg-white rounded-2xl border border-gray-200 shadow-lg">
                  <img
                    src={generateQRCode(selectedCrypto.depositAddress)}
                    alt="Deposit QR Code"
                    className="w-48 h-48 mx-auto"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Scan to copy deposit address
                  </p>
                </div>
              )}
            </div>

            {/* Important Notes */}
            <div className="bg-yellow-50 rounded-2xl p-4 border border-yellow-200">
              <div className="flex items-start space-x-3">
                <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="text-yellow-600 mt-1" />
                <div className="text-sm text-yellow-800">
                  <div className="font-medium mb-1">Important Notes:</div>
                  <ul className="space-y-1 text-xs">
                    <li>• Only send {selectedCrypto.symbol} to this address</li>
                    <li>• Minimum deposit: {formatCurrency(selectedCrypto.minDeposit)}</li>
                    <li>• Deposits require network confirmations</li>
                    <li>• Do not send from smart contracts</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Withdraw Section */}
      {activeTab === 'withdraw' && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center space-x-3 mb-6">
            <div 
              className="w-12 h-12 flex items-center justify-center"
              dangerouslySetInnerHTML={{ 
                __html: assetUtils.getCryptoIcon(selectedCrypto.symbol.toLowerCase()) 
              }}
            />
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                Withdraw {selectedCrypto.symbol}
              </h3>
              <p className="text-sm text-gray-600">
                Available: {formatCurrency(userBalances[selectedCrypto.symbol] || 0)}
              </p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Withdrawal Address */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Withdrawal Address
              </label>
              <input
                type="text"
                value={withdrawAddress}
                onChange={(e) => setWithdrawAddress(e.target.value)}
                placeholder={`Enter ${selectedCrypto.symbol} address`}
                className="w-full p-3 rounded-xl border border-gray-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 font-mono text-sm"
              />
            </div>

            {/* Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={withdrawAmount || ''}
                  onChange={(e) => setWithdrawAmount(Number(e.target.value))}
                  placeholder="0.00"
                  className="w-full p-3 rounded-xl border border-gray-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 text-lg font-medium"
                />
                <button
                  onClick={() => setWithdrawAmount(maxWithdrawAmount)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm font-medium text-purple-600 hover:text-purple-700"
                >
                  MAX
                </button>
              </div>
              
              <div className="flex justify-between text-xs text-gray-500 mt-2">
                <span>Min: {formatCurrency(selectedCrypto.minWithdraw)}</span>
                <span>Max: {formatCurrency(maxWithdrawAmount)}</span>
              </div>
            </div>

            {/* Fee Information */}
            <div className="bg-gray-50 rounded-2xl p-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Withdrawal Amount:</span>
                  <span className="font-medium">{formatCurrency(withdrawAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Network Fee:</span>
                  <span className="font-medium">{formatCurrency(selectedCrypto.withdrawFee)}</span>
                </div>
                <div className="border-t border-gray-200 pt-2 flex justify-between">
                  <span className="font-medium text-gray-900">You'll Receive:</span>
                  <span className="font-bold text-lg">
                    {formatCurrency(Math.max(0, withdrawAmount - selectedCrypto.withdrawFee))}
                  </span>
                </div>
              </div>
            </div>

            {/* Withdraw Button */}
            <button
              onClick={handleWithdraw}
              disabled={!isValidWithdraw}
              className={`w-full py-3 rounded-2xl font-semibold text-white transition-all duration-200 ${
                isValidWithdraw
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:shadow-lg transform hover:scale-105'
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <FontAwesomeIcon icon={ICON_NAMES.WITHDRAW} />
                <span>Withdraw {selectedCrypto.symbol}</span>
              </div>
            </button>

            {/* Security Notice */}
            <div className="bg-blue-50 rounded-2xl p-4 border border-blue-200">
              <div className="flex items-start space-x-3">
                <FontAwesomeIcon icon={ICON_NAMES.SHIELD} className="text-blue-600 mt-1" />
                <div className="text-sm text-blue-800">
                  <div className="font-medium mb-1">Security Notice:</div>
                  <p className="text-xs">
                    Withdrawals are processed within 24 hours. Large amounts may require additional verification.
                    Always double-check the withdrawal address before confirming.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
