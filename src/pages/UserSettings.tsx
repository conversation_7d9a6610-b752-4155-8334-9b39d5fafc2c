/**
 * ==============================================
 * USER SETTINGS PAGE
 * ==============================================
 * 
 * Complete user settings and profile management page with
 * security settings, preferences, and account management.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { colors, components } from '../lib/designSystem';
import { formatCurrency } from '../lib/utils';
import { useAuth } from '../lib/AuthContext';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface UserProfile {
  name: string;
  email: string;
  phone: string;
  country: string;
  timezone: string;
  language: string;
  currency: string;
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  tradingNotifications: boolean;
  loginAlerts: boolean;
}

interface TradingPreferences {
  riskTolerance: 'low' | 'medium' | 'high';
  autoReinvest: boolean;
  maxDailyInvestment: number;
  stopLossPercentage: number;
  takeProfitPercentage: number;
}

export const UserSettings: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'trading' | 'support'>('profile');

  // Get user data from Convex
  const userData = useQuery(api.users.getUserById,
    user ? { userId: user.id } : "skip"
  );

  const [profile, setProfile] = useState<UserProfile>({
    name: '',
    email: '',
    phone: '',
    country: '',
    timezone: '',
    language: 'English',
    currency: 'USD',
  });

  // Update profile state when user data loads
  useEffect(() => {
    if (user && userData) {
      setProfile({
        name: user.name || '',
        email: user.email || '',
        phone: '', // Will be added to user schema later
        country: '', // Will be added to user schema later
        timezone: '', // Will be added to user schema later
        language: 'English',
        currency: 'USD',
      });
    }
  }, [user, userData]);

  const [security, setSecurity] = useState<SecuritySettings>({
    twoFactorEnabled: true,
    emailNotifications: true,
    smsNotifications: false,
    tradingNotifications: true,
    loginAlerts: true,
  });

  const [trading, setTrading] = useState<TradingPreferences>({
    riskTolerance: 'medium',
    autoReinvest: true,
    maxDailyInvestment: 1000,
    stopLossPercentage: 10,
    takeProfitPercentage: 25,
  });

  const handleProfileUpdate = () => {
    // TODO: Implement profile update with Convex mutation
    console.log('Profile updated:', profile);
    // This would call a Convex mutation to update user profile
  };

  const handleSecurityUpdate = () => {
    // TODO: Implement security settings update with Convex mutation
    console.log('Security settings updated:', security);
  };

  const handleTradingUpdate = () => {
    // TODO: Implement trading preferences update with Convex mutation
    console.log('Trading preferences updated:', trading);
  };

  // Loading state
  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-purple-500 animate-spin" />
      </div>
    );
  }

  const tabs = [
    { key: 'profile', label: 'Profile', icon: ICON_NAMES.USER },
    { key: 'security', label: 'Security', icon: ICON_NAMES.SHIELD },
    { key: 'trading', label: 'Trading', icon: ICON_NAMES.SETTINGS },
    { key: 'support', label: 'Support', icon: ICON_NAMES.HELP },
  ];

  return (
    <div className="p-4 md:p-6 pb-32 md:pb-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl md:text-3xl font-black text-gray-900 mb-2">Account Settings</h1>
        <p className="text-gray-600">Manage your profile, security, and trading preferences</p>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-2xl p-2 shadow-lg border border-gray-100">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-3 px-4 rounded-xl font-semibold transition-all flex items-center justify-center space-x-2 ${
                activeTab === tab.key
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
            >
              <FontAwesomeIcon icon={tab.icon} className="text-sm" />
              <span className="hidden sm:inline">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-xl font-bold text-gray-900 mb-6">Profile Information</h3>
          
          <div className="space-y-6">
            {/* Profile Picture */}
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <FontAwesomeIcon icon={ICON_NAMES.USER} className="text-white text-2xl" />
              </div>
              <div>
                <button className="px-4 py-2 bg-purple-500 text-white rounded-xl hover:bg-purple-600 transition-colors">
                  Change Photo
                </button>
                <p className="text-sm text-gray-600 mt-1">JPG, PNG or GIF. Max size 2MB.</p>
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                <input
                  type="text"
                  value={profile.name}
                  onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  value={profile.email}
                  onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input
                  type="tel"
                  value={profile.phone}
                  onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                <select
                  value={profile.country}
                  onChange={(e) => setProfile({ ...profile, country: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="United States">United States</option>
                  <option value="Canada">Canada</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                  <option value="Japan">Japan</option>
                  <option value="Australia">Australia</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                <select
                  value={profile.timezone}
                  onChange={(e) => setProfile({ ...profile, timezone: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="UTC-8 (PST)">UTC-8 (PST)</option>
                  <option value="UTC-5 (EST)">UTC-5 (EST)</option>
                  <option value="UTC+0 (GMT)">UTC+0 (GMT)</option>
                  <option value="UTC+1 (CET)">UTC+1 (CET)</option>
                  <option value="UTC+9 (JST)">UTC+9 (JST)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Currency</label>
                <select
                  value={profile.currency}
                  onChange={(e) => setProfile({ ...profile, currency: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="JPY">JPY - Japanese Yen</option>
                  <option value="CAD">CAD - Canadian Dollar</option>
                </select>
              </div>
            </div>

            <button
              onClick={handleProfileUpdate}
              className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:shadow-lg transition-all"
            >
              Save Changes
            </button>
          </div>
        </div>
      )}

      {/* Security Tab */}
      {activeTab === 'security' && (
        <div className="space-y-6">
          {/* Two-Factor Authentication */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Two-Factor Authentication</h3>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 font-medium">Secure your account with 2FA</p>
                <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={security.twoFactorEnabled}
                  onChange={(e) => setSecurity({ ...security, twoFactorEnabled: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
              </label>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Notification Preferences</h3>
            <div className="space-y-4">
              {[
                { key: 'emailNotifications', label: 'Email Notifications', desc: 'Receive updates via email' },
                { key: 'smsNotifications', label: 'SMS Notifications', desc: 'Receive alerts via text message' },
                { key: 'tradingNotifications', label: 'Trading Alerts', desc: 'Bot performance and trade notifications' },
                { key: 'loginAlerts', label: 'Login Alerts', desc: 'Notify when someone logs into your account' },
              ].map((setting) => (
                <div key={setting.key} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-gray-700 font-medium">{setting.label}</p>
                    <p className="text-sm text-gray-600">{setting.desc}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={security[setting.key as keyof SecuritySettings] as boolean}
                      onChange={(e) => setSecurity({ ...security, [setting.key]: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Password Change */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Change Password</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <button className="px-6 py-3 bg-purple-500 text-white rounded-xl font-semibold hover:bg-purple-600 transition-colors">
                Update Password
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Trading Tab */}
      {activeTab === 'trading' && (
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <h3 className="text-xl font-bold text-gray-900 mb-6">Trading Preferences</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Risk Tolerance</label>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: 'low', label: 'Conservative', color: 'green' },
                  { value: 'medium', label: 'Moderate', color: 'blue' },
                  { value: 'high', label: 'Aggressive', color: 'red' },
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setTrading({ ...trading, riskTolerance: option.value as any })}
                    className={`py-3 px-4 rounded-xl font-medium transition-all ${
                      trading.riskTolerance === option.value
                        ? `bg-${option.color}-500 text-white shadow-lg`
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Daily Investment</label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={trading.maxDailyInvestment}
                    onChange={(e) => setTrading({ ...trading, maxDailyInvestment: Number(e.target.value) })}
                    className="w-full pl-8 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Stop Loss Percentage</label>
                <div className="relative">
                  <input
                    type="number"
                    value={trading.stopLossPercentage}
                    onChange={(e) => setTrading({ ...trading, stopLossPercentage: Number(e.target.value) })}
                    className="w-full pr-8 pl-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between py-3 border-t border-gray-100">
              <div>
                <p className="text-gray-700 font-medium">Auto-Reinvest Profits</p>
                <p className="text-sm text-gray-600">Automatically reinvest bot profits</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={trading.autoReinvest}
                  onChange={(e) => setTrading({ ...trading, autoReinvest: e.target.checked })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
              </label>
            </div>

            <button
              onClick={handleTradingUpdate}
              className="w-full md:w-auto px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:shadow-lg transition-all"
            >
              Save Preferences
            </button>
          </div>
        </div>
      )}

      {/* Support Tab */}
      {activeTab === 'support' && (
        <div className="space-y-6">
          {/* Help Center */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Help Center</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { title: 'Getting Started', desc: 'Learn the basics of bot trading', icon: ICON_NAMES.HELP },
                { title: 'Trading Guides', desc: 'Advanced trading strategies', icon: ICON_NAMES.BOOK },
                { title: 'Security Tips', desc: 'Keep your account secure', icon: ICON_NAMES.SHIELD },
                { title: 'FAQ', desc: 'Frequently asked questions', icon: ICON_NAMES.QUESTION },
              ].map((item) => (
                <div key={item.title} className="p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <FontAwesomeIcon icon={item.icon} className="text-purple-500 text-lg" />
                    <div>
                      <h4 className="font-semibold text-gray-900">{item.title}</h4>
                      <p className="text-sm text-gray-600">{item.desc}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Support */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contact Support</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <input
                  type="text"
                  placeholder="How can we help you?"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                <textarea
                  rows={4}
                  placeholder="Describe your issue or question..."
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:shadow-lg transition-all">
                Send Message
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
