/**
 * ==============================================
 * BOT DETAIL PAGE
 * ==============================================
 * 
 * This component displays detailed information about a specific trading bot,
 * including performance metrics, investment options, and interactive charts.
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../lib/icons';
import { TradingBot } from '../types';
import { formatCurrency, formatPercentage, formatDuration } from '../lib/utils';
interface BotDetailProps {
  onInvest?: (botId: string, amount: number) => void;
  tradingBots: TradingBot[];
  onNavigateToInvestment?: (botId: string) => void;
  onBack?: () => void;
}

export const BotDetail: React.FC<BotDetailProps> = ({ onInvest, tradingBots, onNavigateToInvestment, onBack }) => {
  const [bot, setBot] = useState<TradingBot | null>(null);
  const [investmentAmount, setInvestmentAmount] = useState<number>(1000);
  // Removed modal state as investment flow is now a dedicated page
// const [showInvestModal, setShowInvestModal] = useState(false);

  useEffect(() => {
    // For page-based navigation, we get the bot directly from tradingBots prop
    if (tradingBots && tradingBots.length > 0) {
      const foundBot = tradingBots[0]; // We pass the selected bot as the first item
      setBot(foundBot || null);
    }
  }, [tradingBots]);

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  if (!bot) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="text-4xl text-purple-500 animate-spin mb-4" />
          <p className="text-gray-600">Loading bot details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={handleBack}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="text-gray-600" />
        </button>
        <div>
          <h1 className="text-3xl font-black text-gray-900">{bot.name}</h1>
          <p className="text-gray-600">AI-powered trading bot</p>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Bot Overview */}
          <div className="glass-effect rounded-2xl p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                  <FontAwesomeIcon icon={ICON_NAMES.ROBOT} className="text-white text-2xl" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{bot.name}</h2>
                  <div className="flex items-center space-x-2 mt-1">
                    <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                      bot.risk_level === 'low' ? 'bg-green-100 text-green-600' :
                      bot.risk_level === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-red-100 text-red-600'
                    }`}>
                      {bot.risk_level.toUpperCase()} RISK
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                      bot.is_active ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {bot.is_active ? 'ACTIVE' : 'INACTIVE'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Description</h3>
              <p className="text-gray-600 leading-relaxed">{bot.description}</p>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 mb-3">Target Coins</h3>
              <div className="flex flex-wrap gap-2">
                {bot.target_coins.map(coin => (
                  <span key={coin} className="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-sm font-medium">
                    {coin}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-3">Strategy Details</h3>
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-500 text-sm">Strategy Type</span>
                    <p className="font-semibold text-gray-900 capitalize">{bot.strategy.type}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Stop Loss</span>
                    <p className="font-semibold text-gray-900">{bot.strategy.stop_loss_percentage}%</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Take Profit</span>
                    <p className="font-semibold text-gray-900">{bot.strategy.take_profit_percentage}%</p>
                  </div>
                  <div>
                    <span className="text-gray-500 text-sm">Max Trades/Day</span>
                    <p className="font-semibold text-gray-900">{bot.strategy.max_trades_per_day}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="glass-effect rounded-2xl p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-6">Performance Metrics</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-black text-green-600 mb-2">
                  {formatPercentage(bot.performance.total_return_percentage)}
                </div>
                <div className="text-gray-500 text-sm">Total Return</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-blue-600 mb-2">
                  {formatPercentage(bot.performance.win_rate, false)}
                </div>
                <div className="text-gray-500 text-sm">Win Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-purple-600 mb-2">
                  {bot.performance.sharpe_ratio.toFixed(2)}
                </div>
                <div className="text-gray-500 text-sm">Sharpe Ratio</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black text-orange-600 mb-2">
                  {bot.performance.total_trades}
                </div>
                <div className="text-gray-500 text-sm">Total Trades</div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <span className="text-gray-500 text-sm">7-Day Return</span>
                  <p className="text-xl font-bold text-green-600">
                    {formatPercentage(bot.performance.last_7_days_return)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-500 text-sm">Max Drawdown</span>
                  <p className="text-xl font-bold text-red-600">
                    {formatPercentage(bot.performance.max_drawdown)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-500 text-sm">Avg Trade Duration</span>
                  <p className="text-xl font-bold text-gray-900">
                    {formatDuration(bot.performance.average_trade_duration)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Investment Panel */}
        <div className="space-y-6">
          <div className="glass-effect rounded-2xl p-6 sticky top-6">
            <h3 className="text-lg font-bold text-gray-900 mb-6">Start Investing</h3>
            
            <div className="space-y-4 mb-6">
              <div>
                <span className="text-gray-500 text-sm">Minimum Investment</span>
                <p className="text-xl font-bold text-gray-900">{formatCurrency(bot.min_investment)}</p>
              </div>
              <div>
                <span className="text-gray-500 text-sm">Maximum Investment</span>
                <p className="text-xl font-bold text-gray-900">{formatCurrency(bot.max_investment)}</p>
              </div>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Investment Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <input
                  type="number"
                  value={investmentAmount}
                  onChange={(e) => setInvestmentAmount(Number(e.target.value))}
                  min={bot.min_investment}
                  max={bot.max_investment}
                  className="w-full pl-8 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div className="flex justify-between mt-2">
                <button
                  onClick={() => setInvestmentAmount(bot.min_investment)}
                  className="text-xs text-purple-600 hover:text-purple-700"
                >
                  Min
                </button>
                <button
                  onClick={() => setInvestmentAmount(Math.min(5000, bot.max_investment))}
                  className="text-xs text-purple-600 hover:text-purple-700"
                >
                  $5,000
                </button>
                <button
                  onClick={() => setInvestmentAmount(bot.max_investment)}
                  className="text-xs text-purple-600 hover:text-purple-700"
                >
                  Max
                </button>
              </div>
            </div>

            <button
              onClick={() => {
                if (onNavigateToInvestment) {
                  onNavigateToInvestment(bot.id);
                } else if (onInvest) {
                  onInvest(bot.id, investmentAmount);
                }
              }}
              disabled={!bot.is_active || investmentAmount < bot.min_investment || investmentAmount > bot.max_investment}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-400 disabled:to-gray-400 text-white font-bold py-4 rounded-xl transition-all-smooth disabled:cursor-not-allowed"
            >
              <FontAwesomeIcon icon={ICON_NAMES.PLAY} className="mr-2" />
              {bot.is_active ? 'Start Trading' : 'Bot Inactive'}
            </button>

            <p className="text-xs text-gray-500 mt-4 text-center">
              By investing, you agree to our terms and conditions. Past performance does not guarantee future results.
            </p>
          </div>
        </div>
      </div>


    </div>
  );
};
