/**
 * ==============================================
 * ADMIN USER MANAGEMENT
 * ==============================================
 */

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  is_verified: boolean;
  is_suspended?: boolean;
  suspension_reason?: string;
  suspended_at?: string;
  kyc_status: 'pending' | 'approved' | 'rejected';
  referral_code: string;
  created_at: string;
  last_login?: string;
  portfolios: any[];
  bot_investments: any[];
  referrals_as_referrer: any[];
  referrals_as_referred: any[];
  user_sessions: any[];
  user_activities: any[];
}

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [editingBalance, setEditingBalance] = useState<string | null>(null);
  const [newBalance, setNewBalance] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showActivitiesModal, setShowActivitiesModal] = useState(false);
  const [suspensionReason, setSuspensionReason] = useState('');

  const usersPerPage = 20;

  // Convex queries
  const usersData = useQuery(api.users.getUsers, {
    page: currentPage,
    limit: usersPerPage,
    search: searchTerm || undefined,
  });

  const selectedUserActivities = useQuery(
    api.users.getUserActivities,
    selectedUser ? { userId: selectedUser.id } : "skip"
  );

  const selectedUserSessions = useQuery(
    api.users.getUserSessions,
    selectedUser ? { userId: selectedUser.id } : "skip"
  );

  // Convex mutations
  const updateUserStatus = useMutation(api.users.updateUserStatus);
  const updateUserBalance = useMutation(api.users.updateUserBalance);
  const suspendUser = useMutation(api.users.suspendUser);
  const unsuspendUser = useMutation(api.users.unsuspendUser);
  const forceLogoutUser = useMutation(api.users.forceLogoutUser);

  const users = usersData?.data || [];
  const totalUsers = usersData?.count || 0;
  const loading = usersData === undefined;
  const error = usersData === null ? 'Failed to load users' : '';

  const handleKYCStatusChange = async (userId: string, newStatus: string) => {
    try {
      await updateUserStatus({
        userId: userId as any,
        kyc_status: newStatus as any,
      });
      alert('KYC status updated successfully');
    } catch (err) {
      alert('Failed to update KYC status');
      console.error(err);
    }
  };

  const handleVerificationToggle = async (userId: string, isVerified: boolean) => {
    try {
      await updateUserStatus({
        userId: userId as any,
        is_verified: !isVerified,
      });
      alert('Verification status updated successfully');
    } catch (err) {
      alert('Failed to update verification status');
      console.error(err);
    }
  };

  const handleBalanceUpdate = async (userId: string) => {
    try {
      const balance = parseFloat(newBalance);
      if (isNaN(balance) || balance < 0) {
        alert('Please enter a valid balance amount');
        return;
      }

      await updateUserBalance({
        userId: userId as any,
        newBalance: balance,
      });

      setEditingBalance(null);
      setNewBalance('');
      alert('Balance updated successfully');
    } catch (err) {
      alert('Failed to update balance');
      console.error(err);
    }
  };

  const handleForceLogout = async (userId: string) => {
    try {
      await forceLogoutUser({ userId: userId as any });
      alert('User has been logged out from all sessions');
    } catch (err) {
      alert('Failed to force logout user');
      console.error(err);
    }
  };

  const handleSuspendUser = async (userId: string) => {
    if (!suspensionReason.trim()) {
      alert('Please provide a suspension reason');
      return;
    }

    try {
      await suspendUser({
        userId: userId as any,
        reason: suspensionReason,
      });
      setSuspensionReason('');
      alert('User has been suspended');
    } catch (err) {
      alert('Failed to suspend user');
      console.error(err);
    }
  };

  const handleUnsuspendUser = async (userId: string) => {
    try {
      await unsuspendUser({ userId: userId as any });
      alert('User has been unsuspended');
    } catch (err) {
      alert('Failed to unsuspend user');
      console.error(err);
    }
  };

  const handleViewUserActivities = (user: User) => {
    setSelectedUser(user);
    setShowActivitiesModal(true);
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-400 bg-green-400/20';
      case 'rejected': return 'text-red-400 bg-red-400/20';
      default: return 'text-yellow-400 bg-yellow-400/20';
    }
  };

  const totalPages = Math.ceil(totalUsers / usersPerPage);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">User Management</h1>
          <p className="text-gray-400 mt-1">Manage users, KYC status, verification, and balances</p>
        </div>
        <div className="text-sm text-gray-400">
          Total Users: {totalUsers}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <FontAwesomeIcon 
              icon={ICON_NAMES.SEARCH} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
            <input
              type="text"
              placeholder="Search users by name, email, or username..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>
          <button
            onClick={loadUsers}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <FontAwesomeIcon icon={ICON_NAMES.FILTER} className="mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Users Table */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-blue-500 animate-spin" />
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-400">{error}</p>
            <button 
              onClick={loadUsers}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      KYC Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Verified
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Balance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Last Login
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Referrals
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {users.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-700/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-white">{user.name}</div>
                          <div className="text-sm text-gray-400">{user.email}</div>
                          <div className="text-xs text-gray-500">@{user.username}</div>
                          <div className="text-xs text-purple-400">Code: {user.referral_code}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={user.kyc_status}
                          onChange={(e) => handleKYCStatusChange(user.id, e.target.value)}
                          className={`px-2 py-1 rounded-full text-xs font-medium border-0 bg-gray-700 ${getKYCStatusColor(user.kyc_status)}`}
                        >
                          <option value="pending">Pending</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleVerificationToggle(user.id, user.is_verified)}
                          className={`p-1 rounded ${user.is_verified ? 'text-green-400' : 'text-gray-400'}`}
                        >
                          <FontAwesomeIcon icon={user.is_verified ? ICON_NAMES.CHECK : ICON_NAMES.CLOSE} />
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingBalance === user.id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="number"
                              value={newBalance}
                              onChange={(e) => setNewBalance(e.target.value)}
                              className="w-24 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm"
                              placeholder="0.00"
                            />
                            <button
                              onClick={() => handleBalanceUpdate(user.id)}
                              className="text-green-400 hover:text-green-300"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.SUCCESS} />
                            </button>
                            <button
                              onClick={() => {
                                setEditingBalance(null);
                                setNewBalance('');
                              }}
                              className="text-red-400 hover:text-red-300"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.ERROR} />
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <div className="text-sm text-white">
                              ${user.portfolios?.[0]?.total_balance || '0.00'}
                            </div>
                            <button
                              onClick={() => {
                                setEditingBalance(user.id);
                                setNewBalance(user.portfolios?.[0]?.total_balance || '0');
                              }}
                              className="text-blue-400 hover:text-blue-300"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.EDIT} />
                            </button>
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          {user.bot_investments?.length || 0} investments
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          {user.user_sessions?.[0]?.last_activity
                            ? new Date(user.user_sessions[0].last_activity).toLocaleDateString()
                            : 'Never'
                          }
                        </div>
                        <div className="text-xs text-gray-400">
                          {user.user_sessions?.filter(s => s.is_active).length || 0} active sessions
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          {user.is_suspended ? (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-600">
                              SUSPENDED
                            </span>
                          ) : (
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600">
                              ACTIVE
                            </span>
                          )}
                          {user.suspension_reason && (
                            <span className="text-xs text-gray-400" title={user.suspension_reason}>
                              {user.suspension_reason.length > 20
                                ? user.suspension_reason.substring(0, 20) + '...'
                                : user.suspension_reason
                              }
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">
                          Referred: {user.referrals_as_referrer?.length || 0}
                        </div>
                        <div className="text-xs text-gray-400">
                          {user.referrals_as_referred?.length > 0 ? 'Has referrer' : 'No referrer'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex flex-wrap gap-1">
                          <button
                            onClick={() => handleViewUserActivities(user)}
                            className="text-blue-400 hover:text-blue-300 p-1"
                            title="View Activities"
                          >
                            <FontAwesomeIcon icon={ICON_NAMES.EYE} />
                          </button>
                          <button
                            onClick={() => handleForceLogout(user.id)}
                            className="text-orange-400 hover:text-orange-300 p-1"
                            title="Force Logout"
                          >
                            <FontAwesomeIcon icon={ICON_NAMES.LOGOUT} />
                          </button>
                          {user.is_suspended ? (
                            <button
                              onClick={() => handleUnsuspendUser(user.id)}
                              className="text-green-400 hover:text-green-300 p-1"
                              title="Unsuspend User"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.SUCCESS} />
                            </button>
                          ) : (
                            <button
                              onClick={() => {
                                const reason = prompt('Suspension reason:');
                                if (reason) {
                                  setSuspensionReason(reason);
                                  handleSuspendUser(user.id);
                                }
                              }}
                              className="text-red-400 hover:text-red-300 p-1"
                              title="Suspend User"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.ERROR} />
                            </button>
                          )}
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setShowUserModal(true);
                            }}
                            className="text-purple-400 hover:text-purple-300 p-1"
                            title="View Details"
                          >
                            <FontAwesomeIcon icon={ICON_NAMES.INFO} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-gray-700 px-6 py-3 flex items-center justify-between">
              <div className="text-sm text-gray-400">
                Showing {((currentPage - 1) * usersPerPage) + 1} to {Math.min(currentPage * usersPerPage, totalUsers)} of {totalUsers} users
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-gray-400">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-gray-600 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-500"
                >
                  Next
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* User Details Modal */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">User Details: {selectedUser.name}</h2>
              <button
                onClick={() => {
                  setShowUserModal(false);
                  setSelectedUser(null);
                }}
                className="text-gray-400 hover:text-white"
              >
                <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="h-6 w-6" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* User Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">User Information</h3>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Full Name:</span>
                    <span className="text-white">{selectedUser.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Email:</span>
                    <span className="text-white">{selectedUser.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Username:</span>
                    <span className="text-white">@{selectedUser.username}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Referral Code:</span>
                    <span className="text-purple-400 font-mono">{selectedUser.referral_code}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">KYC Status:</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      selectedUser.kyc_status === 'approved' ? 'bg-green-400/20 text-green-400' :
                      selectedUser.kyc_status === 'rejected' ? 'bg-red-400/20 text-red-400' :
                      'bg-yellow-400/20 text-yellow-400'
                    }`}>
                      {selectedUser.kyc_status.toUpperCase()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Verified:</span>
                    <span className={selectedUser.is_verified ? 'text-green-400' : 'text-red-400'}>
                      {selectedUser.is_verified ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Joined:</span>
                    <span className="text-white">{new Date(selectedUser.created_at).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>

              {/* Portfolio Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">Portfolio & Investments</h3>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Balance:</span>
                    <span className="text-white font-semibold">
                      ${selectedUser.portfolios?.[0]?.total_balance || '0.00'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Available Balance:</span>
                    <span className="text-white">
                      ${selectedUser.portfolios?.[0]?.available_balance || '0.00'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Invested Amount:</span>
                    <span className="text-white">
                      ${selectedUser.portfolios?.[0]?.invested_amount || '0.00'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total P&L:</span>
                    <span className={`font-semibold ${
                      parseFloat(selectedUser.portfolios?.[0]?.total_profit_loss || '0') >= 0
                        ? 'text-green-400' : 'text-red-400'
                    }`}>
                      ${selectedUser.portfolios?.[0]?.total_profit_loss || '0.00'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Active Investments:</span>
                    <span className="text-white">{selectedUser.bot_investments?.length || 0}</span>
                  </div>
                </div>
              </div>

              {/* Referral Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">Referral Information</h3>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Users Referred:</span>
                    <span className="text-white">{selectedUser.referrals_as_referrer?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Referred By:</span>
                    <span className="text-white">
                      {selectedUser.referrals_as_referred?.length > 0 ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Total Commission:</span>
                    <span className="text-green-400">
                      ${selectedUser.referrals_as_referrer?.reduce((sum, ref) => sum + (parseFloat(ref.total_commission) || 0), 0).toFixed(2) || '0.00'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">Quick Actions</h3>

                <div className="space-y-2">
                  <button className="w-full p-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors">
                    View Transaction History
                  </button>
                  <button className="w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded transition-colors">
                    Approve Pending Transactions
                  </button>
                  <button className="w-full p-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition-colors">
                    Send Support Message
                  </button>
                  <button className="w-full p-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors">
                    Suspend Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Activities Modal */}
      {showActivitiesModal && selectedUser && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setShowActivitiesModal(false)}
        >
          <div
            className="bg-gray-800 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto border border-gray-700"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6 border-b border-gray-700">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-white">
                  User Activities - {selectedUser.name}
                </h2>
                <button
                  onClick={() => setShowActivitiesModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-300 rounded-lg transition-colors"
                  title="Close"
                >
                  <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="text-xl" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* User Sessions */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                    Active Sessions ({selectedUserSessions?.filter(s => s.is_active).length || 0})
                  </h3>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {selectedUserSessions && selectedUserSessions.length > 0 ? selectedUserSessions.map((session) => (
                      <div key={session.id} className="bg-gray-700 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="text-sm text-white font-medium">
                              {session.is_active ? (
                                <span className="text-green-400">● Active</span>
                              ) : (
                                <span className="text-gray-400">● Ended</span>
                              )}
                            </div>
                            <div className="text-xs text-gray-400 mt-1">
                              IP: {session.ip_address || 'Unknown'}
                            </div>
                            <div className="text-xs text-gray-400">
                              Started: {new Date(session.created_at).toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-400">
                              Last Activity: {new Date(session.last_activity).toLocaleString()}
                            </div>
                          </div>
                          {session.is_active && (
                            <button
                              onClick={() => handleForceLogout(selectedUser.id)}
                              className="text-red-400 hover:text-red-300 text-xs"
                              title="Force Logout"
                            >
                              <FontAwesomeIcon icon={ICON_NAMES.LOGOUT} />
                            </button>
                          )}
                        </div>
                      </div>
                    )) : (
                      <p className="text-gray-400 text-sm">No sessions found</p>
                    )}
                  </div>
                </div>

                {/* User Activities */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white border-b border-gray-700 pb-2">
                    Recent Activities ({selectedUserActivities?.length || 0})
                  </h3>
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {selectedUserActivities && selectedUserActivities.length > 0 ? selectedUserActivities.map((activity) => (
                      <div key={activity.id} className="bg-gray-700 rounded-lg p-3">
                        <div className="text-sm text-white font-medium">
                          {activity.activity_type}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {activity.description}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {new Date(activity.created_at).toLocaleString()}
                        </div>
                      </div>
                    )) : (
                      <p className="text-gray-400 text-sm">No activities found</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-6 pt-6 border-t border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <button
                    onClick={() => handleForceLogout(selectedUser.id)}
                    className="p-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors text-sm"
                  >
                    Force Logout All
                  </button>
                  {selectedUser.is_suspended ? (
                    <button
                      onClick={() => handleUnsuspendUser(selectedUser.id)}
                      className="p-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm"
                    >
                      Unsuspend User
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        const reason = prompt('Suspension reason:');
                        if (reason) {
                          setSuspensionReason(reason);
                          handleSuspendUser(selectedUser.id);
                        }
                      }}
                      className="p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
                    >
                      Suspend User
                    </button>
                  )}
                  <button className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm">
                    Reset Password
                  </button>
                  <button className="p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm">
                    Send Message
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
