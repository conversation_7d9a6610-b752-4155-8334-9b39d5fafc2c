/**
 * ==============================================
 * ADMIN BOT MANAGEMENT
 * ==============================================
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
// Admin functions now use Convex directly

interface TradingBot {
  id: string;
  name: string;
  description: string;
  strategy: any;
  target_coins: string[];
  risk_level: string;
  min_investment: number;
  max_investment: number;
  performance: any;
  is_active: boolean;
  created_at: string;
  bot_investments: any[];
}

export function BotManagement() {
  const [bots, setBots] = useState<TradingBot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingBot, setEditingBot] = useState<TradingBot | null>(null);

  const [newBot, setNewBot] = useState({
    name: '',
    description: '',
    strategy_type: 'momentum',
    target_coins: '',
    risk_level: 'medium',
    min_investment: 100,
    max_investment: 10000,
    stop_loss_percentage: 5,
    take_profit_percentage: 15,
    max_trades_per_day: 10
  });

  useEffect(() => {
    loadBots();
  }, []);

  const loadBots = async () => {
    try {
      setLoading(true);
      const { data, error } = await getTradingBots();
      
      if (error) {
        setError('Failed to load trading bots');
        console.error('Bots error:', error);
      } else {
        setBots(data || []);
      }
    } catch (err) {
      setError('Failed to load trading bots');
      console.error('Bots error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBot = async () => {
    try {
      const botData = {
        name: newBot.name,
        description: newBot.description,
        strategy: {
          type: newBot.strategy_type,
          parameters: {
            momentum_threshold: 0.05,
            volume_threshold: 1000000
          },
          stop_loss_percentage: newBot.stop_loss_percentage,
          take_profit_percentage: newBot.take_profit_percentage,
          max_trades_per_day: newBot.max_trades_per_day
        },
        target_coins: newBot.target_coins.split(',').map(coin => coin.trim().toUpperCase()),
        risk_level: newBot.risk_level,
        min_investment: newBot.min_investment,
        max_investment: newBot.max_investment,
        performance: {
          total_return_percentage: 0,
          win_rate: 0,
          total_trades: 0,
          profitable_trades: 0,
          average_trade_duration: 0,
          max_drawdown: 0,
          sharpe_ratio: 0,
          last_30_days_return: 0,
          last_7_days_return: 0
        },
        is_active: true
      };

      const { data, error } = await createTradingBot(botData);
      
      if (error) {
        alert('Failed to create trading bot');
      } else {
        setBots([...bots, data]);
        setShowCreateModal(false);
        resetNewBot();
      }
    } catch (err) {
      alert('Failed to create trading bot');
    }
  };

  const handleToggleBotStatus = async (botId: string, currentStatus: boolean) => {
    try {
      const { data, error } = await updateTradingBot(botId, { is_active: !currentStatus });
      
      if (error) {
        alert('Failed to update bot status');
      } else {
        setBots(bots.map(bot => 
          bot.id === botId ? { ...bot, is_active: !currentStatus } : bot
        ));
      }
    } catch (err) {
      alert('Failed to update bot status');
    }
  };

  const resetNewBot = () => {
    setNewBot({
      name: '',
      description: '',
      strategy_type: 'momentum',
      target_coins: '',
      risk_level: 'medium',
      min_investment: 100,
      max_investment: 10000,
      stop_loss_percentage: 5,
      take_profit_percentage: 15,
      max_trades_per_day: 10
    });
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-400 bg-green-400/20';
      case 'high': return 'text-red-400 bg-red-400/20';
      default: return 'text-yellow-400 bg-yellow-400/20';
    }
  };

  const getTotalInvestments = (bot: TradingBot) => {
    return bot.bot_investments?.reduce((sum, inv) => sum + parseFloat(inv.amount || 0), 0) || 0;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Trading Bot Management</h1>
          <p className="text-gray-400 mt-1">Create, manage, and monitor trading bots</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <FontAwesomeIcon icon={ICON_NAMES.PLUS} className="mr-2" />
          Create New Bot
        </button>
      </div>

      {/* Bots Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {loading ? (
          <div className="col-span-full flex items-center justify-center h-64">
            <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-blue-500 animate-spin" />
          </div>
        ) : error ? (
          <div className="col-span-full bg-red-500/20 border border-red-400/30 rounded-lg p-4">
            <p className="text-red-200">{error}</p>
            <button 
              onClick={loadBots}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        ) : (
          bots.map((bot) => (
            <div key={bot.id} className="bg-gray-800 rounded-lg border border-gray-700 p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-white">{bot.name}</h3>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(bot.risk_level)}`}>
                    {bot.risk_level.toUpperCase()} RISK
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleBotStatus(bot.id, bot.is_active)}
                    className={`p-2 rounded-lg ${bot.is_active ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-600 hover:bg-gray-700'} transition-colors`}
                  >
                    <FontAwesomeIcon icon={bot.is_active ? ICON_NAMES.PLAY : ICON_NAMES.PAUSE} className="text-white" />
                  </button>
                  <button className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                    <FontAwesomeIcon icon={ICON_NAMES.EDIT} className="text-white" />
                  </button>
                </div>
              </div>

              <p className="text-gray-400 text-sm mb-4">{bot.description}</p>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Target Coins:</span>
                  <span className="text-white text-sm">
                    {Array.isArray(bot.target_coins) ? bot.target_coins.join(', ') : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Investment Range:</span>
                  <span className="text-white text-sm">
                    ${bot.min_investment} - ${bot.max_investment}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Invested:</span>
                  <span className="text-white text-sm font-medium">
                    ${getTotalInvestments(bot).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Active Investors:</span>
                  <span className="text-white text-sm">
                    {bot.bot_investments?.length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Performance:</span>
                  <span className={`text-sm font-medium ${
                    (bot.performance?.total_return_percentage || 0) >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {(bot.performance?.total_return_percentage || 0).toFixed(1)}%
                  </span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-700">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Status:</span>
                  <span className={`font-medium ${bot.is_active ? 'text-green-400' : 'text-gray-400'}`}>
                    {bot.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create Bot Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Create New Trading Bot</h2>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  resetNewBot();
                }}
                className="text-gray-400 hover:text-white"
              >
                <FontAwesomeIcon icon={ICON_NAMES.CLOSE} className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Bot Name</label>
                <input
                  type="text"
                  value={newBot.name}
                  onChange={(e) => setNewBot({...newBot, name: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  placeholder="e.g., Advanced Momentum Bot"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                <textarea
                  value={newBot.description}
                  onChange={(e) => setNewBot({...newBot, description: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white h-20"
                  placeholder="Describe the bot's strategy and purpose..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Strategy Type</label>
                  <select
                    value={newBot.strategy_type}
                    onChange={(e) => setNewBot({...newBot, strategy_type: e.target.value})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="momentum">Momentum</option>
                    <option value="arbitrage">Arbitrage</option>
                    <option value="scalping">Scalping</option>
                    <option value="swing">Swing Trading</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Risk Level</label>
                  <select
                    value={newBot.risk_level}
                    onChange={(e) => setNewBot({...newBot, risk_level: e.target.value})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Target Coins (comma separated)</label>
                <input
                  type="text"
                  value={newBot.target_coins}
                  onChange={(e) => setNewBot({...newBot, target_coins: e.target.value})}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  placeholder="e.g., BTC, ETH, DOGE, SHIB"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Min Investment ($)</label>
                  <input
                    type="number"
                    value={newBot.min_investment}
                    onChange={(e) => setNewBot({...newBot, min_investment: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Investment ($)</label>
                  <input
                    type="number"
                    value={newBot.max_investment}
                    onChange={(e) => setNewBot({...newBot, max_investment: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Stop Loss (%)</label>
                  <input
                    type="number"
                    value={newBot.stop_loss_percentage}
                    onChange={(e) => setNewBot({...newBot, stop_loss_percentage: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Take Profit (%)</label>
                  <input
                    type="number"
                    value={newBot.take_profit_percentage}
                    onChange={(e) => setNewBot({...newBot, take_profit_percentage: parseFloat(e.target.value)})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Max Trades/Day</label>
                  <input
                    type="number"
                    value={newBot.max_trades_per_day}
                    onChange={(e) => setNewBot({...newBot, max_trades_per_day: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                  />
                </div>
              </div>
            </div>

            <div className="flex space-x-4 mt-6">
              <button
                onClick={handleCreateBot}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Create Bot
              </button>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  resetNewBot();
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
