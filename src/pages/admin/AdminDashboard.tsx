/**
 * ==============================================
 * ADMIN DASHBOARD
 * ==============================================
 */

import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";

interface DashboardStats {
  totalUsers: number;
  totalBots: number;
  activeTrades: number;
  totalTransactions: number;
  totalValue: number;
  recentTrades: any[];
}

export function AdminDashboard() {
  // Use Convex queries
  const adminStats = useQuery(api.admin.getAdminStats);
  const platformMetrics = useQuery(api.admin.getPlatformMetrics);

  const loading = adminStats === undefined || platformMetrics === undefined;
  const error = adminStats === null || platformMetrics === null ? 'Failed to load dashboard data' : '';

  // Transform data to match expected interface
  const stats: DashboardStats | null = adminStats ? {
    totalUsers: adminStats.users.total,
    totalBots: 0, // Will be implemented when bot management is added
    activeTrades: adminStats.investments.active,
    totalTransactions: adminStats.transactions.total,
    totalValue: adminStats.transactions.totalVolume,
    recentTrades: [], // Will be implemented when trade data is available
  } : null;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-blue-500 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4">
        <p className="text-red-200">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Users',
      value: stats?.totalUsers || 0,
      icon: ICON_NAMES.USER,
      color: 'blue',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: 'Trading Bots',
      value: stats?.totalBots || 0,
      icon: ICON_NAMES.ROBOT,
      color: 'purple',
      change: '+2',
      changeType: 'increase'
    },
    {
      name: 'Active Trades',
      value: stats?.activeTrades || 0,
      icon: ICON_NAMES.CHART,
      color: 'green',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: 'Total Value',
      value: `$${(stats?.totalValue || 0).toLocaleString()}`,
      icon: ICON_NAMES.WALLET,
      color: 'yellow',
      change: '+15%',
      changeType: 'increase'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Welcome to Admin Dashboard</h1>
        <p className="text-blue-100">Monitor and manage your MemeBot Pro platform</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm font-medium">{stat.name}</p>
                <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full ${
                stat.color === 'blue' ? 'bg-blue-600/20' :
                stat.color === 'purple' ? 'bg-purple-600/20' :
                stat.color === 'green' ? 'bg-green-600/20' :
                'bg-yellow-600/20'
              }`}>
                <FontAwesomeIcon 
                  icon={stat.icon} 
                  className={`h-6 w-6 ${
                    stat.color === 'blue' ? 'text-blue-400' :
                    stat.color === 'purple' ? 'text-purple-400' :
                    stat.color === 'green' ? 'text-green-400' :
                    'text-yellow-400'
                  }`} 
                />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <FontAwesomeIcon
                icon={stat.changeType === 'increase' ? ICON_NAMES.ARROW_UP : ICON_NAMES.ARROW_DOWN}
                className={`h-4 w-4 ${stat.changeType === 'increase' ? 'text-green-400' : 'text-red-400'} mr-1`}
              />
              <span className={`text-sm font-medium ${stat.changeType === 'increase' ? 'text-green-400' : 'text-red-400'}`}>
                {stat.change}
              </span>
              <span className="text-gray-400 text-sm ml-1">from last month</span>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Trades */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Recent Trades</h3>
          </div>
          <div className="p-6">
            {stats?.recentTrades && stats.recentTrades.length > 0 ? (
              <div className="space-y-4">
                {stats.recentTrades.slice(0, 5).map((trade, index) => (
                  <div key={index} className="flex items-center justify-between py-2">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${trade.trade_type === 'buy' ? 'bg-green-400' : 'bg-red-400'}`}></div>
                      <div>
                        <p className="text-white font-medium">{trade.coin_symbol}</p>
                        <p className="text-gray-400 text-sm">{trade.users?.name || 'Unknown User'}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-white font-medium">${trade.total_value}</p>
                      <p className="text-gray-400 text-sm">{trade.trade_type.toUpperCase()}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 text-center py-8">No recent trades</p>
            )}
          </div>
        </div>

        {/* System Status */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">System Status</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Database</span>
                <span className="flex items-center text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Online
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Trading Engine</span>
                <span className="flex items-center text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">API Services</span>
                <span className="flex items-center text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                  Operational
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Backup System</span>
                <span className="flex items-center text-yellow-400">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                  Scheduled
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg text-white font-medium transition-colors">
            Add New Bot
          </button>
          <button className="p-4 bg-green-600 hover:bg-green-700 rounded-lg text-white font-medium transition-colors">
            Export Reports
          </button>
          <button className="p-4 bg-purple-600 hover:bg-purple-700 rounded-lg text-white font-medium transition-colors">
            System Backup
          </button>
        </div>
      </div>
    </div>
  );
}
