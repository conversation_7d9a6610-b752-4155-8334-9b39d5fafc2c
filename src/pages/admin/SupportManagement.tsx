/**
 * ==============================================
 * ADMIN SUPPORT CHAT MANAGEMENT
 * ==============================================
 */

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ICON_NAMES } from '../../lib/icons';
// Support functions now use Convex directly

interface SupportChat {
  id: string;
  user_id: string;
  admin_id: string | null;
  status: string;
  priority: string;
  subject: string;
  created_at: string;
  updated_at: string;
  users: {
    name: string;
    email: string;
    username: string;
  };
  admin_users: {
    name: string;
  } | null;
  support_messages: {
    message: string;
    sender_type: string;
    created_at: string;
  }[];
}

export function SupportManagement() {
  const [chats, setChats] = useState<SupportChat[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      setLoading(true);
      const { data, error } = await getSupportChats();
      
      if (error) {
        setError('Failed to load support chats');
        console.error('Support chats error:', error);
      } else {
        setChats(data || []);
      }
    } catch (err) {
      setError('Failed to load support chats');
      console.error('Support chats error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!selectedChat || !newMessage.trim()) return;

    try {
      setSendingMessage(true);
      const { data, error } = await sendSupportMessage(selectedChat.id, 'admin-id', newMessage);
      
      if (error) {
        alert('Failed to send message');
      } else {
        // Update local state
        setSelectedChat({
          ...selectedChat,
          support_messages: [
            ...selectedChat.support_messages,
            {
              message: newMessage,
              sender_type: 'admin',
              created_at: new Date().toISOString()
            }
          ]
        });
        setNewMessage('');
      }
    } catch (err) {
      alert('Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-400 bg-red-400/20';
      case 'high': return 'text-orange-400 bg-orange-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/20';
      default: return 'text-green-400 bg-green-400/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-green-400 bg-green-400/20';
      case 'pending': return 'text-yellow-400 bg-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Support Chat Management</h1>
          <p className="text-gray-400 mt-1">Manage customer support conversations</p>
        </div>
        <div className="text-sm text-gray-400">
          Total Chats: {chats.length}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* Chat List */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Support Chats</h3>
          </div>
          <div className="overflow-y-auto h-full">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="h-8 w-8 text-blue-500 animate-spin" />
              </div>
            ) : error ? (
              <div className="p-4 text-center">
                <p className="text-red-400">{error}</p>
                <button 
                  onClick={loadChats}
                  className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                >
                  Retry
                </button>
              </div>
            ) : (
              <div className="space-y-2 p-4">
                {chats.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => setSelectedChat(chat)}
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${
                      selectedChat?.id === chat.id ? 'bg-blue-600/20 border border-blue-500/30' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="text-white font-medium text-sm">{chat.users?.name}</h4>
                        <p className="text-gray-400 text-xs">{chat.users?.email}</p>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(chat.priority)}`}>
                          {chat.priority.toUpperCase()}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(chat.status)}`}>
                          {chat.status.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <p className="text-gray-300 text-sm font-medium mb-2">{chat.subject}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400 text-xs">
                        {chat.support_messages?.length || 0} messages
                      </span>
                      <span className="text-gray-400 text-xs">
                        {new Date(chat.updated_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Chat Messages */}
        <div className="lg:col-span-2 bg-gray-800 rounded-lg border border-gray-700 flex flex-col">
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white">{selectedChat.subject}</h3>
                    <p className="text-gray-400 text-sm">
                      {selectedChat.users?.name} ({selectedChat.users?.email})
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(selectedChat.priority)}`}>
                      {selectedChat.priority.toUpperCase()}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedChat.status)}`}>
                      {selectedChat.status.toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {selectedChat.support_messages?.map((message, index) => (
                  <div
                    key={index}
                    className={`flex ${message.sender_type === 'admin' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender_type === 'admin'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-700 text-gray-200'
                      }`}
                    >
                      <p className="text-sm">{message.message}</p>
                      <p className={`text-xs mt-1 ${
                        message.sender_type === 'admin' ? 'text-blue-200' : 'text-gray-400'
                      }`}>
                        {new Date(message.created_at).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="p-4 border-t border-gray-700">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type your response..."
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || sendingMessage}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    {sendingMessage ? (
                      <FontAwesomeIcon icon={ICON_NAMES.LOADING} className="animate-spin" />
                    ) : (
                      <FontAwesomeIcon icon={ICON_NAMES.SEND} />
                    )}
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FontAwesomeIcon icon={ICON_NAMES.CHAT} className="h-16 w-16 text-gray-600 mb-4" />
                <p className="text-gray-400">Select a chat to view messages</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Support Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Open Chats</p>
              <p className="text-2xl font-bold text-green-400">
                {chats.filter(chat => chat.status === 'open').length}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.CHAT} className="h-8 w-8 text-green-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Pending Chats</p>
              <p className="text-2xl font-bold text-yellow-400">
                {chats.filter(chat => chat.status === 'pending').length}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.WARNING} className="h-8 w-8 text-yellow-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">High Priority</p>
              <p className="text-2xl font-bold text-red-400">
                {chats.filter(chat => chat.priority === 'high' || chat.priority === 'urgent').length}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.ALERT} className="h-8 w-8 text-red-400" />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Closed Today</p>
              <p className="text-2xl font-bold text-blue-400">
                {chats.filter(chat => 
                  chat.status === 'closed' && 
                  new Date(chat.updated_at).toDateString() === new Date().toDateString()
                ).length}
              </p>
            </div>
            <FontAwesomeIcon icon={ICON_NAMES.CHECK} className="h-8 w-8 text-blue-400" />
          </div>
        </div>
      </div>
    </div>
  );
}
