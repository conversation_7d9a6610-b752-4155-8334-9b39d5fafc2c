/**
 * ==============================================
 * AUTHENTICATION FUNCTIONS
 * ==============================================
 * 
 * This module provides authentication functions using Convex.
 * Includes user registration, login, password hashing, and session management.
 */

import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Hash password using a simple hash function
 * In production, use bcrypt or similar
 */
function hashPassword(password: string): string {
  // Simple hash for demo - in production use bcrypt
  let hash = 0;
  for (let i = 0; i < password.length; i++) {
    const char = password.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Generate a unique referral code
 */
function generateReferralCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

/**
 * Register a new user
 */
export const registerUser = mutation({
  args: {
    email: v.string(),
    username: v.string(),
    name: v.string(),
    password: v.string(),
    referralCode: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUserByEmail = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUserByEmail) {
      throw new Error("User with this email already exists");
    }

    const existingUserByUsername = await ctx.db
      .query("users")
      .withIndex("by_username", (q) => q.eq("username", args.username))
      .first();

    if (existingUserByUsername) {
      throw new Error("Username already taken");
    }

    // Hash password
    const passwordHash = hashPassword(args.password);
    
    // Generate unique referral code
    let referralCode = generateReferralCode();
    let existingCode = await ctx.db
      .query("users")
      .withIndex("by_referral_code", (q) => q.eq("referral_code", referralCode))
      .first();
    
    while (existingCode) {
      referralCode = generateReferralCode();
      existingCode = await ctx.db
        .query("users")
        .withIndex("by_referral_code", (q) => q.eq("referral_code", referralCode))
        .first();
    }

    const now = new Date().toISOString();

    // Create user
    const userId = await ctx.db.insert("users", {
      email: args.email,
      username: args.username,
      name: args.name,
      password_hash: passwordHash,
      is_verified: false,
      is_suspended: false,
      kyc_status: "pending",
      referral_code: referralCode,
      referred_by: args.referralCode || undefined,
      created_at: now,
      updated_at: now,
    });

    // Create initial portfolio
    await ctx.db.insert("portfolios", {
      user_id: userId,
      total_balance: 0,
      available_balance: 0,
      invested_balance: 0,
      total_profit_loss: 0,
      currency: "USD",
      created_at: now,
      updated_at: now,
    });

    // Log registration activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "registration",
      description: "User registered successfully",
      created_at: now,
    });

    return {
      success: true,
      userId,
      message: "User registered successfully",
    };
  },
});

/**
 * Login user
 */
export const loginUser = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Find user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (!user) {
      throw new Error("Invalid email or password");
    }

    // Check if user is suspended
    if (user.is_suspended) {
      throw new Error("Account is suspended. Please contact support.");
    }

    // Verify password
    const passwordHash = hashPassword(args.password);
    if (user.password_hash !== passwordHash) {
      throw new Error("Invalid email or password");
    }

    const now = new Date().toISOString();

    // Update last login
    await ctx.db.patch(user._id, {
      last_login: now,
      updated_at: now,
    });

    // Create session
    const sessionId = await ctx.db.insert("user_sessions", {
      user_id: user._id,
      ip_address: args.ipAddress,
      user_agent: args.userAgent,
      is_active: true,
      created_at: now,
      last_activity: now,
    });

    // Log login activity
    await ctx.db.insert("user_activities", {
      user_id: user._id,
      activity_type: "login",
      description: "User logged in successfully",
      metadata: {
        ip_address: args.ipAddress,
        user_agent: args.userAgent,
      },
      created_at: now,
    });

    return {
      success: true,
      user: {
        id: user._id,
        email: user.email,
        username: user.username,
        name: user.name,
        is_verified: user.is_verified,
        is_admin: user.is_admin || false,
        admin_role: user.admin_role,
        admin_permissions: user.admin_permissions || [],
      },
      sessionId,
      message: "Login successful",
    };
  },
});

/**
 * Get user by ID
 */
export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) {
      return null;
    }

    return {
      id: user._id,
      email: user.email,
      username: user.username,
      name: user.name,
      is_verified: user.is_verified,
      is_suspended: user.is_suspended,
      is_admin: user.is_admin || false,
      admin_role: user.admin_role,
      admin_permissions: user.admin_permissions || [],
      created_at: user.created_at,
      last_login: user.last_login,
    };
  },
});

/**
 * Logout user (end session)
 */
export const logoutUser = mutation({
  args: { sessionId: v.id("user_sessions") },
  handler: async (ctx, args) => {
    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const now = new Date().toISOString();

    // End session
    await ctx.db.patch(args.sessionId, {
      is_active: false,
      ended_at: now,
    });

    // Log logout activity
    await ctx.db.insert("user_activities", {
      user_id: session.user_id,
      activity_type: "logout",
      description: "User logged out",
      created_at: now,
    });

    return { success: true, message: "Logged out successfully" };
  },
});

/**
 * Create admin user (for initial setup)
 */
export const createAdminUser = mutation({
  args: {
    email: v.string(),
    username: v.string(),
    name: v.string(),
    password: v.string(),
    role: v.optional(v.string()),
    permissions: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    // Check if admin already exists
    const existingAdmin = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingAdmin) {
      throw new Error("Admin with this email already exists");
    }

    const passwordHash = hashPassword(args.password);
    const referralCode = generateReferralCode();
    const now = new Date().toISOString();

    const adminId = await ctx.db.insert("users", {
      email: args.email,
      username: args.username,
      name: args.name,
      password_hash: passwordHash,
      is_verified: true,
      is_suspended: false,
      kyc_status: "approved",
      referral_code: referralCode,
      is_admin: true,
      admin_role: args.role || "super_admin",
      admin_permissions: args.permissions || [
        "user_management",
        "transaction_management",
        "bot_management",
        "system_settings",
        "audit_logs",
      ],
      created_at: now,
      updated_at: now,
    });

    return {
      success: true,
      adminId,
      message: "Admin user created successfully",
    };
  },
});
