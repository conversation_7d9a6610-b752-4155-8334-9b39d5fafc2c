import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Get all active trading bots
 */
export const getTradingBots = query({
  args: {
    riskLevel: v.optional(v.union(v.literal("low"), v.literal("medium"), v.literal("high"), v.literal("all"))),
    sortBy: v.optional(v.union(v.literal("performance"), v.literal("capacity"), v.literal("name"))),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("trading_bots")
      .filter((q) => q.eq(q.field("is_active"), true));

    // Filter by risk level if specified
    if (args.riskLevel && args.riskLevel !== "all") {
      query = query.filter((q) => q.eq(q.field("risk_level"), args.riskLevel));
    }

    let bots = await query.collect();

    // Sort the results
    if (args.sortBy) {
      bots.sort((a, b) => {
        switch (args.sortBy) {
          case "performance":
            return b.performance_30d - a.performance_30d;
          case "capacity":
            return (b.capacity - b.used) - (a.capacity - a.used);
          case "name":
            return a.name.localeCompare(b.name);
          default:
            return 0;
        }
      });
    }

    return bots;
  },
});

/**
 * Get a specific trading bot by ID
 */
export const getTradingBotById = query({
  args: { botId: v.id("trading_bots") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.botId);
  },
});

/**
 * Initialize default trading bots
 */
export const initializeDefaultTradingBots = mutation({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();
    
    const defaultBots = [
      {
        name: 'Conservative Growth',
        description: 'Steady, low-risk growth with focus on established meme coins',
        risk_level: 'low' as const,
        min_investment: 100,
        max_investment: 10000,
        performance_30d: 8.5,
        performance_90d: 24.2,
        performance_1y: 156.8,
        win_rate: 78.5,
        total_trades: 1247,
        avg_trade_duration: '4.2 hours',
        capacity: 500000,
        used: 342000,
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        name: 'Momentum Trader',
        description: 'Captures trending meme coins with medium risk tolerance',
        risk_level: 'medium' as const,
        min_investment: 250,
        max_investment: 25000,
        performance_30d: 15.3,
        performance_90d: 42.7,
        performance_1y: 284.5,
        win_rate: 65.2,
        total_trades: 2156,
        avg_trade_duration: '2.8 hours',
        capacity: 750000,
        used: 523000,
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        name: 'Aggressive Alpha',
        description: 'High-risk, high-reward strategy for maximum gains',
        risk_level: 'high' as const,
        min_investment: 500,
        max_investment: 50000,
        performance_30d: 28.7,
        performance_90d: 89.4,
        performance_1y: 567.2,
        win_rate: 52.8,
        total_trades: 3892,
        avg_trade_duration: '1.5 hours',
        capacity: 1000000,
        used: 789000,
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        name: 'Scalp Master',
        description: 'Quick scalping strategy for frequent small profits',
        risk_level: 'medium' as const,
        min_investment: 200,
        max_investment: 15000,
        performance_30d: 12.1,
        performance_90d: 35.8,
        performance_1y: 198.3,
        win_rate: 71.4,
        total_trades: 5647,
        avg_trade_duration: '45 minutes',
        capacity: 400000,
        used: 267000,
        is_active: true,
        created_at: now,
        updated_at: now,
      },
    ];

    const results = [];
    for (const bot of defaultBots) {
      // Check if already exists
      const existing = await ctx.db
        .query("trading_bots")
        .filter((q) => q.eq(q.field("name"), bot.name))
        .first();
      
      if (!existing) {
        const id = await ctx.db.insert("trading_bots", bot);
        results.push({ name: bot.name, id });
      }
    }
    
    return {
      success: true,
      message: `Initialized ${results.length} trading bots`,
      created: results,
    };
  },
});

/**
 * Add a new trading bot (admin only)
 */
export const addTradingBot = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    risk_level: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    min_investment: v.number(),
    max_investment: v.number(),
    capacity: v.number(),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    return await ctx.db.insert("trading_bots", {
      ...args,
      performance_30d: 0,
      performance_90d: 0,
      performance_1y: 0,
      win_rate: 0,
      total_trades: 0,
      avg_trade_duration: "0 minutes",
      used: 0,
      is_active: true,
      created_at: now,
      updated_at: now,
    });
  },
});

/**
 * Update trading bot performance (system only)
 */
export const updateTradingBotPerformance = mutation({
  args: {
    botId: v.id("trading_bots"),
    performance_30d: v.optional(v.number()),
    performance_90d: v.optional(v.number()),
    performance_1y: v.optional(v.number()),
    win_rate: v.optional(v.number()),
    total_trades: v.optional(v.number()),
    avg_trade_duration: v.optional(v.string()),
    used: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { botId, ...updates } = args;
    
    return await ctx.db.patch(botId, {
      ...updates,
      updated_at: new Date().toISOString(),
    });
  },
});
