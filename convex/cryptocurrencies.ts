import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Get all supported cryptocurrencies
 */
export const getSupportedCryptocurrencies = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("supported_cryptocurrencies")
      .filter((q) => q.eq(q.field("is_active"), true))
      .order("asc")
      .collect();
  },
});

/**
 * Get a specific cryptocurrency by symbol
 */
export const getCryptocurrencyBySymbol = query({
  args: { symbol: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("supported_cryptocurrencies")
      .filter((q) => 
        q.and(
          q.eq(q.field("symbol"), args.symbol),
          q.eq(q.field("is_active"), true)
        )
      )
      .first();
  },
});

/**
 * Add a new supported cryptocurrency (admin only)
 */
export const addSupportedCryptocurrency = mutation({
  args: {
    symbol: v.string(),
    name: v.string(),
    network: v.string(),
    min_deposit: v.number(),
    min_withdraw: v.number(),
    withdraw_fee: v.number(),
    deposit_address: v.string(),
  },
  handler: async (ctx, args) => {
    const now = new Date().toISOString();
    
    return await ctx.db.insert("supported_cryptocurrencies", {
      ...args,
      is_active: true,
      created_at: now,
      updated_at: now,
    });
  },
});

/**
 * Update a supported cryptocurrency (admin only)
 */
export const updateSupportedCryptocurrency = mutation({
  args: {
    id: v.id("supported_cryptocurrencies"),
    symbol: v.optional(v.string()),
    name: v.optional(v.string()),
    network: v.optional(v.string()),
    min_deposit: v.optional(v.number()),
    min_withdraw: v.optional(v.number()),
    withdraw_fee: v.optional(v.number()),
    deposit_address: v.optional(v.string()),
    is_active: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    return await ctx.db.patch(id, {
      ...updates,
      updated_at: new Date().toISOString(),
    });
  },
});

/**
 * Initialize default cryptocurrencies
 */
export const initializeDefaultCryptocurrencies = mutation({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();
    
    const defaultCryptos = [
      {
        symbol: 'USDT',
        name: 'Tether',
        network: 'TRC20',
        min_deposit: 10,
        min_withdraw: 20,
        withdraw_fee: 1,
        deposit_address: 'TQrZ9dxpKKKKKKKKKKKKKKKKKKKKKKKKKK',
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        symbol: 'USDC',
        name: 'USD Coin',
        network: 'ERC20',
        min_deposit: 10,
        min_withdraw: 20,
        withdraw_fee: 2,
        deposit_address: '******************************************',
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        network: 'Bitcoin',
        min_deposit: 0.001,
        min_withdraw: 0.002,
        withdraw_fee: 0.0005,
        deposit_address: '******************************************',
        is_active: true,
        created_at: now,
        updated_at: now,
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        network: 'ERC20',
        min_deposit: 0.01,
        min_withdraw: 0.02,
        withdraw_fee: 0.005,
        deposit_address: '******************************************',
        is_active: true,
        created_at: now,
        updated_at: now,
      },
    ];

    const results = [];
    for (const crypto of defaultCryptos) {
      // Check if already exists
      const existing = await ctx.db
        .query("supported_cryptocurrencies")
        .filter((q) => q.eq(q.field("symbol"), crypto.symbol))
        .first();
      
      if (!existing) {
        const id = await ctx.db.insert("supported_cryptocurrencies", crypto);
        results.push({ symbol: crypto.symbol, id });
      }
    }
    
    return {
      success: true,
      message: `Initialized ${results.length} cryptocurrencies`,
      created: results,
    };
  },
});
