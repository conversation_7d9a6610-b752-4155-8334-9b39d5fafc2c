import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Helper function to verify admin permissions
 */
async function verifyAdminPermission(ctx: any, adminId: string, requiredPermission: string) {
  const admin = await ctx.db.get(adminId);
  if (!admin || !admin.is_admin) {
    throw new Error("Unauthorized: Admin access required");
  }
  
  // Check if admin has required permission
  if (!admin.admin_permissions?.includes(requiredPermission) && admin.admin_role !== "super_admin") {
    throw new Error(`Unauthorized: ${requiredPermission} permission required`);
  }

  return admin;
}

// ==============================================
// USER QUERIES
// ==============================================

export const getUsers = query({
  args: {
    adminId: v.id("users"),
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    search: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Verify admin permissions
    await verifyAdminPermission(ctx, args.adminId, "user_management");

    const { page = 1, limit = 20, search = "" } = args;
    
    let users = await ctx.db.query("users").collect();
    
    // Filter by search term if provided
    if (search) {
      users = users.filter(user => 
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.username.toLowerCase().includes(search.toLowerCase()) ||
        user.name.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    // Get related data for each user
    const usersWithData = await Promise.all(
      users.map(async (user) => {
        const portfolio = await ctx.db
          .query("portfolios")
          .withIndex("by_user", (q) => q.eq("user_id", user._id))
          .first();
        
        const investments = await ctx.db
          .query("bot_investments")
          .withIndex("by_user", (q) => q.eq("user_id", user._id))
          .collect();
        
        const sessions = await ctx.db
          .query("user_sessions")
          .withIndex("by_user", (q) => q.eq("user_id", user._id))
          .order("desc")
          .take(5);
        
        const activities = await ctx.db
          .query("user_activities")
          .withIndex("by_user", (q) => q.eq("user_id", user._id))
          .order("desc")
          .take(10);
        
        const referralsAsReferrer = await ctx.db
          .query("referrals")
          .withIndex("by_referrer", (q) => q.eq("referrer_id", user._id))
          .collect();
        
        const referralsAsReferred = await ctx.db
          .query("referrals")
          .withIndex("by_referred", (q) => q.eq("referred_id", user._id))
          .collect();
        
        return {
          ...user,
          portfolios: portfolio ? [portfolio] : [],
          bot_investments: investments,
          user_sessions: sessions,
          user_activities: activities,
          referrals_as_referrer: referralsAsReferrer,
          referrals_as_referred: referralsAsReferred,
        };
      })
    );
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = usersWithData.slice(startIndex, endIndex);
    
    return {
      data: paginatedUsers,
      count: users.length,
      page,
      limit,
    };
  },
});

export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    if (!user) return null;
    
    const portfolio = await ctx.db
      .query("portfolios")
      .withIndex("by_user", (q) => q.eq("user_id", user._id))
      .first();
    
    const investments = await ctx.db
      .query("bot_investments")
      .withIndex("by_user", (q) => q.eq("user_id", user._id))
      .collect();
    
    const sessions = await ctx.db
      .query("user_sessions")
      .withIndex("by_user", (q) => q.eq("user_id", user._id))
      .order("desc")
      .collect();
    
    const activities = await ctx.db
      .query("user_activities")
      .withIndex("by_user", (q) => q.eq("user_id", user._id))
      .order("desc")
      .collect();
    
    return {
      ...user,
      portfolio,
      investments,
      sessions,
      activities,
    };
  },
});

export const getUserActivities = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { userId, limit = 50 } = args;
    
    return await ctx.db
      .query("user_activities")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .order("desc")
      .take(limit);
  },
});

export const getUserSessions = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("user_sessions")
      .withIndex("by_user", (q) => q.eq("user_id", args.userId))
      .order("desc")
      .collect();
  },
});

// ==============================================
// USER MUTATIONS
// ==============================================

export const updateUserStatus = mutation({
  args: {
    userId: v.id("users"),
    is_verified: v.optional(v.boolean()),
    kyc_status: v.optional(v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected"))),
  },
  handler: async (ctx, args) => {
    const { userId, ...updates } = args;
    
    return await ctx.db.patch(userId, {
      ...updates,
      updated_at: new Date().toISOString(),
    });
  },
});

export const updateUserBalance = mutation({
  args: {
    userId: v.id("users"),
    newBalance: v.number(),
  },
  handler: async (ctx, args) => {
    const { userId, newBalance } = args;
    
    const portfolio = await ctx.db
      .query("portfolios")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .first();
    
    if (!portfolio) {
      // Create new portfolio if doesn't exist
      return await ctx.db.insert("portfolios", {
        user_id: userId,
        total_balance: newBalance,
        available_balance: newBalance,
        invested_balance: 0,
        total_profit_loss: 0,
        currency: "USDT",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    } else {
      // Update existing portfolio
      return await ctx.db.patch(portfolio._id, {
        total_balance: newBalance,
        available_balance: newBalance,
        updated_at: new Date().toISOString(),
      });
    }
  },
});

export const suspendUser = mutation({
  args: {
    userId: v.id("users"),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const { userId, reason } = args;
    
    // Update user status
    await ctx.db.patch(userId, {
      is_suspended: true,
      suspension_reason: reason,
      suspended_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    // Force logout all sessions
    const sessions = await ctx.db
      .query("user_sessions")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .filter((q) => q.eq(q.field("is_active"), true))
      .collect();
    
    for (const session of sessions) {
      await ctx.db.patch(session._id, {
        is_active: false,
        ended_at: new Date().toISOString(),
      });
    }
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "account_suspended",
      description: `Account suspended: ${reason}`,
      created_at: new Date().toISOString(),
    });
    
    return { success: true };
  },
});

export const unsuspendUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const { userId } = args;
    
    await ctx.db.patch(userId, {
      is_suspended: false,
      suspension_reason: undefined,
      suspended_at: undefined,
      updated_at: new Date().toISOString(),
    });
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "account_unsuspended",
      description: "Account unsuspended",
      created_at: new Date().toISOString(),
    });
    
    return { success: true };
  },
});

export const forceLogoutUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const { userId } = args;
    
    const sessions = await ctx.db
      .query("user_sessions")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .filter((q) => q.eq(q.field("is_active"), true))
      .collect();
    
    for (const session of sessions) {
      await ctx.db.patch(session._id, {
        is_active: false,
        ended_at: new Date().toISOString(),
      });
    }
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "force_logout",
      description: "Forced logout from all sessions",
      created_at: new Date().toISOString(),
    });
    
    return { success: true };
  },
});
