import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// ==============================================
// ADMIN AUDIT QUERIES
// ==============================================

export const getAdminAuditLogs = query({
  args: {
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    adminId: v.optional(v.id("users")),
    action: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { page = 1, limit = 50, adminId, action } = args;
    
    let logs: any[];

    if (adminId) {
      logs = await ctx.db
        .query("admin_audit_logs")
        .withIndex("by_admin", (q) => q.eq("admin_id", adminId))
        .collect();
    } else {
      logs = await ctx.db.query("admin_audit_logs").collect();
    }

    // Filter by action if provided
    if (action) {
      logs = logs.filter((log: any) => log.action === action);
    }

    // Get admin data for each log
    const logsWithAdmins = await Promise.all(
      logs.map(async (log: any) => {
        const admin = await ctx.db.get(log.admin_id);
        return {
          ...log,
          admin: admin ? {
            id: admin._id,
            email: (admin as any).email,
            username: (admin as any).username,
            name: (admin as any).name,
          } : null,
        };
      })
    );
    
    // Sort by created_at desc
    logsWithAdmins.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLogs = logsWithAdmins.slice(startIndex, endIndex);
    
    return {
      data: paginatedLogs,
      count: logs.length,
      page,
      limit,
    };
  },
});

export const getAdminStats = query({
  args: {},
  handler: async (ctx) => {
    const [users, transactions, investments, sessions] = await Promise.all([
      ctx.db.query("users").collect(),
      ctx.db.query("transactions").collect(),
      ctx.db.query("bot_investments").collect(),
      ctx.db.query("user_sessions").collect(),
    ]);
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    const stats = {
      users: {
        total: users.length,
        verified: users.filter(u => u.is_verified).length,
        suspended: users.filter(u => u.is_suspended).length,
        newToday: users.filter(u => new Date(u.created_at) >= today).length,
        newThisWeek: users.filter(u => new Date(u.created_at) >= thisWeek).length,
        newThisMonth: users.filter(u => new Date(u.created_at) >= thisMonth).length,
      },
      transactions: {
        total: transactions.length,
        pending: transactions.filter(t => t.status === "pending").length,
        completed: transactions.filter(t => t.status === "completed").length,
        failed: transactions.filter(t => t.status === "failed").length,
        totalVolume: transactions
          .filter(t => t.status === "completed")
          .reduce((sum, t) => sum + t.amount, 0),
        todayVolume: transactions
          .filter(t => t.status === "completed" && new Date(t.created_at) >= today)
          .reduce((sum, t) => sum + t.amount, 0),
      },
      investments: {
        total: investments.length,
        active: investments.filter(i => i.is_active).length,
        totalValue: investments.reduce((sum, i) => sum + i.current_value, 0),
        totalProfit: investments.reduce((sum, i) => sum + i.profit_loss, 0),
      },
      sessions: {
        total: sessions.length,
        active: sessions.filter(s => s.is_active).length,
        todayLogins: sessions.filter(s => new Date(s.created_at) >= today).length,
      },
    };
    
    return stats;
  },
});

export const getPlatformMetrics = query({
  args: {},
  handler: async (ctx) => {
    const [users, transactions, investments] = await Promise.all([
      ctx.db.query("users").collect(),
      ctx.db.query("transactions").collect(),
      ctx.db.query("bot_investments").collect(),
    ]);
    
    // Calculate metrics for the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    const recentUsers = users.filter(u => new Date(u.created_at) >= thirtyDaysAgo);
    const recentTransactions = transactions.filter(t => new Date(t.created_at) >= thirtyDaysAgo);
    const recentInvestments = investments.filter(i => new Date(i.created_at) >= thirtyDaysAgo);
    
    // Group by day for charts
    const dailyMetrics: any[] = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayUsers = recentUsers.filter(u => 
        u.created_at.startsWith(dateStr)
      ).length;
      
      const dayTransactions = recentTransactions.filter(t => 
        t.created_at.startsWith(dateStr) && t.status === "completed"
      );
      
      const dayVolume = dayTransactions.reduce((sum, t) => sum + t.amount, 0);
      
      const dayInvestments = recentInvestments.filter(i => 
        i.created_at.startsWith(dateStr)
      ).length;
      
      dailyMetrics.push({
        date: dateStr,
        users: dayUsers,
        transactions: dayTransactions.length,
        volume: dayVolume,
        investments: dayInvestments,
      });
    }
    
    return {
      dailyMetrics,
      summary: {
        totalUsers: users.length,
        totalTransactions: transactions.length,
        totalVolume: transactions
          .filter(t => t.status === "completed")
          .reduce((sum, t) => sum + t.amount, 0),
        totalInvestments: investments.length,
        totalInvestmentValue: investments.reduce((sum, i) => sum + i.current_value, 0),
      },
    };
  },
});

// ==============================================
// ADMIN AUDIT MUTATIONS
// ==============================================

export const logAdminAction = mutation({
  args: {
    adminId: v.id("users"),
    action: v.string(),
    targetType: v.optional(v.string()),
    targetId: v.optional(v.string()),
    details: v.string(),
    ipAddress: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { adminId, action, targetType, targetId, details, ipAddress } = args;
    
    return await ctx.db.insert("admin_audit_logs", {
      admin_id: adminId,
      action,
      target_type: targetType,
      target_id: targetId,
      details,
      ip_address: ipAddress,
      created_at: new Date().toISOString(),
    });
  },
});

export const createUser = mutation({
  args: {
    email: v.string(),
    username: v.string(),
    name: v.string(),
    password_hash: v.string(),
    is_verified: v.optional(v.boolean()),
    kyc_status: v.optional(v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected"))),
    adminId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const { adminId, email, username, name, password_hash, is_verified = false, kyc_status = "pending" } = args;
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();
    
    if (existingUser) {
      throw new Error("User with this email already exists");
    }
    
    // Generate referral code
    const referralCode = Math.random().toString(36).substring(2, 8).toUpperCase();
    
    // Create user
    const userId = await ctx.db.insert("users", {
      email,
      username,
      name,
      password_hash,
      is_verified,
      kyc_status,
      referral_code: referralCode,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    // Create initial portfolio
    await ctx.db.insert("portfolios", {
      user_id: userId,
      total_balance: 0,
      available_balance: 0,
      invested_balance: 0,
      total_profit_loss: 0,
      currency: "USDT",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    // Log admin action
    await ctx.db.insert("admin_audit_logs", {
      admin_id: adminId,
      action: "create_user",
      target_type: "user",
      target_id: userId,
      details: `Created user: ${email} (${username})`,
      created_at: new Date().toISOString(),
    });
    
    // Log user activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "account_created",
      description: "Account created by admin",
      created_at: new Date().toISOString(),
    });
    
    return userId;
  },
});

export const deleteUser = mutation({
  args: {
    userId: v.id("users"),
    adminId: v.id("users"),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const { userId, adminId, reason } = args;
    
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }
    
    // Delete related data
    const [portfolio, investments, sessions, activities, transactions] = await Promise.all([
      ctx.db.query("portfolios").withIndex("by_user", (q) => q.eq("user_id", userId)).collect(),
      ctx.db.query("bot_investments").withIndex("by_user", (q) => q.eq("user_id", userId)).collect(),
      ctx.db.query("user_sessions").withIndex("by_user", (q) => q.eq("user_id", userId)).collect(),
      ctx.db.query("user_activities").withIndex("by_user", (q) => q.eq("user_id", userId)).collect(),
      ctx.db.query("transactions").withIndex("by_user", (q) => q.eq("user_id", userId)).collect(),
    ]);
    
    // Delete all related records
    for (const record of [...portfolio, ...investments, ...sessions, ...activities, ...transactions]) {
      await ctx.db.delete(record._id);
    }
    
    // Delete user
    await ctx.db.delete(userId);
    
    // Log admin action
    await ctx.db.insert("admin_audit_logs", {
      admin_id: adminId,
      action: "delete_user",
      target_type: "user",
      target_id: userId,
      details: `Deleted user: ${user.email} (${user.username}). Reason: ${reason}`,
      created_at: new Date().toISOString(),
    });
    
    return { success: true };
  },
});
