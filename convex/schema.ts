import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Users table
  users: defineTable({
    email: v.string(),
    username: v.string(),
    name: v.string(),
    password_hash: v.string(),
    is_verified: v.boolean(),
    is_suspended: v.optional(v.boolean()),
    suspension_reason: v.optional(v.string()),
    suspended_at: v.optional(v.string()),
    kyc_status: v.union(v.literal("pending"), v.literal("approved"), v.literal("rejected")),
    referral_code: v.string(),
    referred_by: v.optional(v.string()),
    is_admin: v.optional(v.boolean()),
    admin_role: v.optional(v.string()),
    admin_permissions: v.optional(v.array(v.string())),
    created_at: v.string(),
    updated_at: v.string(),
    last_login: v.optional(v.string()),
  })
    .index("by_email", ["email"])
    .index("by_username", ["username"])
    .index("by_referral_code", ["referral_code"])
    .index("by_admin", ["is_admin"]),

  // User sessions for tracking logins
  user_sessions: defineTable({
    user_id: v.id("users"),
    ip_address: v.optional(v.string()),
    user_agent: v.optional(v.string()),
    is_active: v.boolean(),
    created_at: v.string(),
    last_activity: v.string(),
    ended_at: v.optional(v.string()),
  })
    .index("by_user", ["user_id"])
    .index("by_active", ["is_active"]),

  // User activities for audit trail
  user_activities: defineTable({
    user_id: v.id("users"),
    activity_type: v.string(),
    description: v.string(),
    metadata: v.optional(v.any()),
    created_at: v.string(),
  })
    .index("by_user", ["user_id"])
    .index("by_type", ["activity_type"]),

  // User portfolios/balances
  portfolios: defineTable({
    user_id: v.id("users"),
    total_balance: v.number(),
    available_balance: v.number(),
    invested_balance: v.number(),
    total_profit_loss: v.number(),
    currency: v.string(),
    created_at: v.string(),
    updated_at: v.string(),
  })
    .index("by_user", ["user_id"]),

  // Trading bots
  trading_bots: defineTable({
    name: v.string(),
    description: v.string(),
    creator_id: v.optional(v.id("users")),
    strategy_type: v.string(),
    risk_level: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    min_investment: v.number(),
    max_investment: v.number(),
    expected_return: v.number(),
    win_rate: v.number(),
    is_active: v.boolean(),
    is_public: v.boolean(),
    performance_data: v.any(),
    configuration: v.any(),
    created_at: v.string(),
    updated_at: v.string(),
  })
    .index("by_creator", ["creator_id"])
    .index("by_active", ["is_active"])
    .index("by_public", ["is_public"]),

  // Bot investments
  bot_investments: defineTable({
    user_id: v.id("users"),
    bot_id: v.id("trading_bots"),
    amount: v.number(),
    current_value: v.number(),
    profit_loss: v.number(),
    profit_loss_percentage: v.number(),
    is_active: v.boolean(),
    auto_reinvest: v.boolean(),
    created_at: v.string(),
    updated_at: v.string(),
  })
    .index("by_user", ["user_id"])
    .index("by_bot", ["bot_id"])
    .index("by_active", ["is_active"]),

  // Transactions
  transactions: defineTable({
    user_id: v.id("users"),
    type: v.union(
      v.literal("deposit"),
      v.literal("withdrawal"),
      v.literal("investment"),
      v.literal("profit"),
      v.literal("loss"),
      v.literal("fee")
    ),
    amount: v.number(),
    currency: v.string(),
    status: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    ),
    description: v.string(),
    metadata: v.optional(v.any()),
    created_at: v.string(),
    updated_at: v.string(),
  })
    .index("by_user", ["user_id"])
    .index("by_type", ["type"])
    .index("by_status", ["status"]),

  // Admin audit logs
  admin_audit_logs: defineTable({
    admin_id: v.id("users"),
    action: v.string(),
    target_type: v.optional(v.string()),
    target_id: v.optional(v.string()),
    details: v.string(),
    ip_address: v.optional(v.string()),
    created_at: v.string(),
  })
    .index("by_admin", ["admin_id"])
    .index("by_action", ["action"]),

  // Referrals
  referrals: defineTable({
    referrer_id: v.id("users"),
    referred_id: v.id("users"),
    referral_code: v.string(),
    commission_earned: v.number(),
    total_commission: v.number(),
    status: v.union(v.literal("active"), v.literal("inactive")),
    created_at: v.string(),
  })
    .index("by_referrer", ["referrer_id"])
    .index("by_referred", ["referred_id"])
    .index("by_code", ["referral_code"]),
});
