/**
 * ==============================================
 * SETUP FUNCTIONS
 * ==============================================
 * 
 * This module provides setup functions for initializing the database
 * with default data, admin users, and system configuration.
 */

import { mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Hash password using a simple hash function
 * In production, use bcrypt or similar
 */
function hashPassword(password: string): string {
  // Simple hash for demo - in production use bcrypt
  let hash = 0;
  for (let i = 0; i < password.length; i++) {
    const char = password.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}

/**
 * Generate a unique referral code
 */
function generateReferralCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

/**
 * Initialize the database with default admin user and sample data
 */
export const initializeDatabase = mutation({
  args: {},
  handler: async (ctx) => {
    const now = new Date().toISOString();

    // Check if admin already exists
    const existingAdmin = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (existingAdmin) {
      return {
        success: false,
        message: "Database already initialized",
      };
    }

    // Create default admin user
    const adminId = await ctx.db.insert("users", {
      email: "<EMAIL>",
      username: "admin",
      name: "System Administrator",
      password_hash: hashPassword("admin123"),
      is_verified: true,
      is_suspended: false,
      kyc_status: "approved",
      referral_code: generateReferralCode(),
      is_admin: true,
      admin_role: "super_admin",
      admin_permissions: [
        "user_management",
        "transaction_management",
        "bot_management",
        "system_settings",
        "audit_logs",
        "financial_oversight",
        "support_management",
      ],
      created_at: now,
      updated_at: now,
    });

    // Create admin portfolio
    await ctx.db.insert("portfolios", {
      user_id: adminId,
      total_balance: 0,
      available_balance: 0,
      invested_balance: 0,
      total_profit_loss: 0,
      currency: "USD",
      created_at: now,
      updated_at: now,
    });

    // Create sample trading bots
    const bot1Id = await ctx.db.insert("trading_bots", {
      name: "Conservative Growth Bot",
      description: "A low-risk trading bot focused on steady, conservative growth with minimal volatility.",
      strategy_type: "conservative",
      risk_level: "low",
      min_investment: 100,
      max_investment: 10000,
      expected_return: 8.5,
      win_rate: 78.2,
      is_active: true,
      is_public: true,
      performance_data: {
        total_trades: 1247,
        winning_trades: 975,
        losing_trades: 272,
        avg_profit: 2.3,
        max_drawdown: 4.1,
        sharpe_ratio: 1.8,
      },
      configuration: {
        stop_loss: 2.0,
        take_profit: 3.5,
        max_positions: 5,
        trading_pairs: ["BTC/USDT", "ETH/USDT", "SOL/USDT"],
      },
      created_at: now,
      updated_at: now,
    });

    const bot2Id = await ctx.db.insert("trading_bots", {
      name: "Aggressive Scalper",
      description: "High-frequency trading bot for experienced traders seeking maximum returns with higher risk.",
      strategy_type: "scalping",
      risk_level: "high",
      min_investment: 500,
      max_investment: 50000,
      expected_return: 24.7,
      win_rate: 65.8,
      is_active: true,
      is_public: true,
      performance_data: {
        total_trades: 3892,
        winning_trades: 2561,
        losing_trades: 1331,
        avg_profit: 1.8,
        max_drawdown: 12.3,
        sharpe_ratio: 2.4,
      },
      configuration: {
        stop_loss: 1.5,
        take_profit: 2.0,
        max_positions: 15,
        trading_pairs: ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT"],
      },
      created_at: now,
      updated_at: now,
    });

    const bot3Id = await ctx.db.insert("trading_bots", {
      name: "Balanced Portfolio Bot",
      description: "Medium-risk bot that balances growth potential with risk management for steady returns.",
      strategy_type: "balanced",
      risk_level: "medium",
      min_investment: 250,
      max_investment: 25000,
      expected_return: 15.3,
      win_rate: 71.5,
      is_active: true,
      is_public: true,
      performance_data: {
        total_trades: 2156,
        winning_trades: 1541,
        losing_trades: 615,
        avg_profit: 2.1,
        max_drawdown: 7.8,
        sharpe_ratio: 2.1,
      },
      configuration: {
        stop_loss: 2.5,
        take_profit: 4.0,
        max_positions: 8,
        trading_pairs: ["BTC/USDT", "ETH/USDT", "SOL/USDT", "MATIC/USDT"],
      },
      created_at: now,
      updated_at: now,
    });

    // Log initialization
    await ctx.db.insert("user_activities", {
      user_id: adminId,
      activity_type: "system_initialization",
      description: "Database initialized with default admin user and sample data",
      metadata: {
        bots_created: [bot1Id, bot2Id, bot3Id],
        admin_permissions: [
          "user_management",
          "transaction_management",
          "bot_management",
          "system_settings",
          "audit_logs",
          "financial_oversight",
          "support_management",
        ],
      },
      created_at: now,
    });

    return {
      success: true,
      message: "Database initialized successfully",
      admin_credentials: {
        email: "<EMAIL>",
        password: "admin123",
      },
      data: {
        admin_id: adminId,
        bots_created: 3,
      },
    };
  },
});

/**
 * Reset database (for development only)
 */
export const resetDatabase = mutation({
  args: { confirm: v.boolean() },
  handler: async (ctx, args) => {
    if (!args.confirm) {
      throw new Error("Database reset requires confirmation");
    }

    // Delete all data (in reverse dependency order)
    const tables = [
      "user_activities",
      "user_sessions", 
      "bot_investments",
      "transactions",
      "referrals",
      "portfolios",
      "trading_bots",
      "users",
    ];

    let deletedCount = 0;
    for (const table of tables) {
      const records = await ctx.db.query(table as any).collect();
      for (const record of records) {
        await ctx.db.delete(record._id);
        deletedCount++;
      }
    }

    return {
      success: true,
      message: `Database reset complete. Deleted ${deletedCount} records.`,
    };
  },
});
