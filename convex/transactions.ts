import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// ==============================================
// TRANSACTION QUERIES
// ==============================================

export const getTransactions = query({
  args: {
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    userId: v.optional(v.id("users")),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { page = 1, limit = 20, userId, type, status } = args;
    
    let transactions: any[];

    // Apply filters
    if (userId) {
      transactions = await ctx.db
        .query("transactions")
        .withIndex("by_user", (q) => q.eq("user_id", userId))
        .collect();
    } else {
      transactions = await ctx.db.query("transactions").collect();
    }

    // Additional filtering
    if (type) {
      transactions = transactions.filter((t: any) => t.type === type);
    }

    if (status) {
      transactions = transactions.filter((t: any) => t.status === status);
    }

    // Get user data for each transaction
    const transactionsWithUsers = await Promise.all(
      transactions.map(async (transaction: any) => {
        const user = await ctx.db.get(transaction.user_id);
        return {
          ...transaction,
          user: user ? {
            id: user._id,
            email: (user as any).email,
            username: (user as any).username,
            name: (user as any).name,
          } : null,
        };
      })
    );
    
    // Sort by created_at desc
    transactionsWithUsers.sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
    
    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTransactions = transactionsWithUsers.slice(startIndex, endIndex);
    
    return {
      data: paginatedTransactions,
      count: transactions.length,
      page,
      limit,
    };
  },
});

export const getTransactionById = query({
  args: { transactionId: v.id("transactions") },
  handler: async (ctx, args) => {
    const transaction = await ctx.db.get(args.transactionId);
    if (!transaction) return null;
    
    const user = await ctx.db.get(transaction.user_id);
    
    return {
      ...transaction,
      user: user ? {
        id: user._id,
        email: user.email,
        username: user.username,
        name: user.name,
      } : null,
    };
  },
});

export const getTransactionStats = query({
  args: {},
  handler: async (ctx) => {
    const transactions = await ctx.db.query("transactions").collect();
    
    const stats = {
      total: transactions.length,
      pending: transactions.filter(t => t.status === "pending").length,
      completed: transactions.filter(t => t.status === "completed").length,
      failed: transactions.filter(t => t.status === "failed").length,
      totalVolume: transactions
        .filter(t => t.status === "completed")
        .reduce((sum, t) => sum + t.amount, 0),
      deposits: transactions.filter(t => t.type === "deposit").length,
      withdrawals: transactions.filter(t => t.type === "withdrawal").length,
      investments: transactions.filter(t => t.type === "investment").length,
    };
    
    return stats;
  },
});

// ==============================================
// TRANSACTION MUTATIONS
// ==============================================

export const createTransaction = mutation({
  args: {
    userId: v.id("users"),
    type: v.union(
      v.literal("deposit"),
      v.literal("withdrawal"),
      v.literal("investment"),
      v.literal("profit"),
      v.literal("loss"),
      v.literal("fee")
    ),
    amount: v.number(),
    currency: v.string(),
    description: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const { userId, type, amount, currency, description, metadata } = args;
    
    const transactionId = await ctx.db.insert("transactions", {
      user_id: userId,
      type,
      amount,
      currency,
      status: "pending",
      description,
      metadata,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "transaction_created",
      description: `${type} transaction created: ${amount} ${currency}`,
      metadata: { transactionId, type, amount, currency },
      created_at: new Date().toISOString(),
    });
    
    return transactionId;
  },
});

export const updateTransactionStatus = mutation({
  args: {
    transactionId: v.id("transactions"),
    status: v.union(
      v.literal("pending"),
      v.literal("completed"),
      v.literal("failed"),
      v.literal("cancelled")
    ),
    adminNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { transactionId, status, adminNote } = args;
    
    const transaction = await ctx.db.get(transactionId);
    if (!transaction) {
      throw new Error("Transaction not found");
    }
    
    // Update transaction
    await ctx.db.patch(transactionId, {
      status,
      updated_at: new Date().toISOString(),
      ...(adminNote && { 
        metadata: { 
          ...transaction.metadata, 
          adminNote 
        } 
      }),
    });
    
    // If completed, update user balance
    if (status === "completed") {
      const portfolio = await ctx.db
        .query("portfolios")
        .withIndex("by_user", (q) => q.eq("user_id", transaction.user_id))
        .first();
      
      if (portfolio) {
        let balanceChange = 0;
        
        if (transaction.type === "deposit") {
          balanceChange = transaction.amount;
        } else if (transaction.type === "withdrawal") {
          balanceChange = -transaction.amount;
        }
        
        if (balanceChange !== 0) {
          await ctx.db.patch(portfolio._id, {
            total_balance: portfolio.total_balance + balanceChange,
            available_balance: portfolio.available_balance + balanceChange,
            updated_at: new Date().toISOString(),
          });
        }
      }
    }
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: transaction.user_id,
      activity_type: "transaction_updated",
      description: `Transaction ${status}: ${transaction.amount} ${transaction.currency}`,
      metadata: { transactionId, status, type: transaction.type },
      created_at: new Date().toISOString(),
    });
    
    return { success: true };
  },
});

export const processDeposit = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    paymentMethod: v.string(),
    transactionHash: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { userId, amount, currency, paymentMethod, transactionHash } = args;
    
    // Create deposit transaction
    const transactionId = await ctx.db.insert("transactions", {
      user_id: userId,
      type: "deposit",
      amount,
      currency,
      status: "completed", // Auto-approve for admin
      description: `Deposit via ${paymentMethod}`,
      metadata: { paymentMethod, transactionHash },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    // Update user balance
    const portfolio = await ctx.db
      .query("portfolios")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .first();
    
    if (portfolio) {
      await ctx.db.patch(portfolio._id, {
        total_balance: portfolio.total_balance + amount,
        available_balance: portfolio.available_balance + amount,
        updated_at: new Date().toISOString(),
      });
    } else {
      // Create new portfolio
      await ctx.db.insert("portfolios", {
        user_id: userId,
        total_balance: amount,
        available_balance: amount,
        invested_balance: 0,
        total_profit_loss: 0,
        currency,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: "deposit_processed",
      description: `Deposit processed: ${amount} ${currency}`,
      metadata: { transactionId, amount, currency, paymentMethod },
      created_at: new Date().toISOString(),
    });
    
    return transactionId;
  },
});

export const processWithdrawal = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    withdrawalAddress: v.string(),
    approve: v.boolean(),
  },
  handler: async (ctx, args) => {
    const { userId, amount, currency, withdrawalAddress, approve } = args;
    
    const portfolio = await ctx.db
      .query("portfolios")
      .withIndex("by_user", (q) => q.eq("user_id", userId))
      .first();
    
    if (!portfolio || portfolio.available_balance < amount) {
      throw new Error("Insufficient balance");
    }
    
    const status = approve ? "completed" : "pending";
    
    // Create withdrawal transaction
    const transactionId = await ctx.db.insert("transactions", {
      user_id: userId,
      type: "withdrawal",
      amount,
      currency,
      status,
      description: `Withdrawal to ${withdrawalAddress}`,
      metadata: { withdrawalAddress },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
    
    if (approve) {
      // Update user balance
      await ctx.db.patch(portfolio._id, {
        total_balance: portfolio.total_balance - amount,
        available_balance: portfolio.available_balance - amount,
        updated_at: new Date().toISOString(),
      });
    }
    
    // Log activity
    await ctx.db.insert("user_activities", {
      user_id: userId,
      activity_type: approve ? "withdrawal_processed" : "withdrawal_pending",
      description: `Withdrawal ${approve ? "processed" : "pending"}: ${amount} ${currency}`,
      metadata: { transactionId, amount, currency, withdrawalAddress },
      created_at: new Date().toISOString(),
    });
    
    return transactionId;
  },
});
