{"name": "memecoin-trading-bot-platform", "private": true, "version": "1.0.0", "type": "module", "description": "A crypto meme coin bot trading platform with automated trading bots", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/web3.js": "^1.98.2", "@types/react-router-dom": "^5.3.3", "@wagmi/core": "^2.6.5", "chart.js": "^4.4.1", "convex": "^1.25.2", "ethers": "^6.10.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^7.6.2", "viem": "^2.7.15", "wagmi": "^2.5.7", "web3": "^4.6.0", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "3.3.7", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}